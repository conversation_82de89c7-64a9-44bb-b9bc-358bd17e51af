#!/usr/bin/env python3
"""
ChatGPT批量测试脚本
支持大规模自动化安全测试
"""

import asyncio
import json
import time
from pathlib import Path
from test_chatgpt_existing_browser import ExistingBrowserChatGPTTester
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchChatGPTTester(ExistingBrowserChatGPTTester):
    """批量ChatGPT测试器"""
    
    def __init__(self, debug_port=9222):
        super().__init__(debug_port)
        self.test_stats = {
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'upload_success_count': 0,
            'saved_files_count': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def run_full_batch_tests(self, data_folder="data_folder", 
                                  max_tests_per_type=None, 
                                  test_interval=3,
                                  save_progress=True):
        """
        运行完整批量测试
        
        Args:
            data_folder: 测试数据文件夹
            max_tests_per_type: 每个风险类型最大测试数量（None表示全部）
            test_interval: 测试间隔（秒）
            save_progress: 是否保存进度
        """
        results = []
        data_path = Path(data_folder)
        
        if not data_path.exists():
            logger.error(f"测试数据文件夹不存在: {data_folder}")
            return []
        
        self.test_stats['start_time'] = time.time()
        
        logger.info(f"🚀 开始批量测试")
        logger.info(f"📁 数据文件夹: {data_folder}")
        logger.info(f"⏱️ 测试间隔: {test_interval}秒")
        
        # 收集所有测试用例
        all_test_cases = []
        for risk_type_folder in data_path.iterdir():
            if risk_type_folder.is_dir():
                risk_type = risk_type_folder.name
                test_cases = list(risk_type_folder.iterdir())
                
                # 限制每个类型的测试数量
                if max_tests_per_type:
                    test_cases = test_cases[:max_tests_per_type]
                
                for test_case in test_cases:
                    if test_case.is_dir():
                        all_test_cases.append((risk_type, test_case))
        
        total_cases = len(all_test_cases)
        logger.info(f"📊 总测试用例数: {total_cases}")
        
        # 按风险类型统计
        risk_type_counts = {}
        for risk_type, _ in all_test_cases:
            risk_type_counts[risk_type] = risk_type_counts.get(risk_type, 0) + 1
        
        logger.info("📋 测试用例分布:")
        for risk_type, count in risk_type_counts.items():
            logger.info(f"  {risk_type}: {count} 个")
        
        # 执行测试
        for i, (risk_type, test_case_folder) in enumerate(all_test_cases, 1):
            logger.info(f"\n🧪 [{i}/{total_cases}] 测试: {risk_type}/{test_case_folder.name}")

            try:
                # 每次测试前都开始新对话，确保测试独立性
                logger.info("🔄 开始新对话以确保测试独立性...")
                new_chat_success = await self.start_new_chat()
                if new_chat_success:
                    logger.info("✅ 新对话已开始")
                else:
                    logger.warning("⚠️ 新对话开始失败，可能影响测试结果")

                result = await self.run_single_test(test_case_folder)
                results.append(result)

                # 更新统计
                self.test_stats['total_tests'] += 1
                if result.get('test_success'):
                    self.test_stats['successful_tests'] += 1
                else:
                    self.test_stats['failed_tests'] += 1

                if result.get('upload_success'):
                    self.test_stats['upload_success_count'] += 1

                if result.get('saved_file'):
                    self.test_stats['saved_files_count'] += 1

                # 显示进度
                success_rate = self.test_stats['successful_tests'] / self.test_stats['total_tests'] * 100
                new_chat_rate = sum(1 for r in results if r.get('new_chat_started', False)) / len(results) * 100
                logger.info(f"✅ 进度: {i}/{total_cases} ({i/total_cases*100:.1f}%), 成功率: {success_rate:.1f}%, 新对话率: {new_chat_rate:.1f}%")

                # 保存中间进度
                if save_progress and i % 10 == 0:
                    await self.save_progress(results, f"batch_progress_{i}.json")

            except Exception as e:
                logger.error(f"❌ 测试失败: {e}")
                self.test_stats['failed_tests'] += 1
                results.append({
                    'test_folder': str(test_case_folder),
                    'test_case_id': test_case_folder.name,
                    'risk_type': risk_type,
                    'test_success': False,
                    'error': str(e),
                    'new_chat_started': False
                })

            # 测试间隔
            if i < total_cases:  # 最后一个测试不需要等待
                await asyncio.sleep(test_interval)
        
        self.test_stats['end_time'] = time.time()
        
        # 保存最终结果
        await self.save_final_results(results)
        
        return results
    
    async def save_progress(self, results, filename):
        """保存测试进度"""
        try:
            progress_data = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'stats': self.test_stats.copy(),
                'results': results
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 进度已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    async def save_final_results(self, results):
        """保存最终结果"""
        try:
            # 计算测试时长
            duration = self.test_stats['end_time'] - self.test_stats['start_time']
            
            final_data = {
                'test_summary': {
                    'total_tests': self.test_stats['total_tests'],
                    'successful_tests': self.test_stats['successful_tests'],
                    'failed_tests': self.test_stats['failed_tests'],
                    'success_rate': self.test_stats['successful_tests'] / self.test_stats['total_tests'] * 100 if self.test_stats['total_tests'] > 0 else 0,
                    'upload_success_count': self.test_stats['upload_success_count'],
                    'saved_files_count': self.test_stats['saved_files_count'],
                    'test_duration_seconds': duration,
                    'test_duration_formatted': f"{int(duration//3600):02d}:{int((duration%3600)//60):02d}:{int(duration%60):02d}",
                    'start_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.test_stats['start_time'])),
                    'end_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.test_stats['end_time']))
                },
                'risk_type_stats': self.calculate_risk_type_stats(results),
                'detailed_results': results
            }
            
            # 保存详细结果
            filename = f"batch_test_results_{int(self.test_stats['start_time'])}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 最终结果已保存: {filename}")
            
            # 生成简要报告
            await self.generate_summary_report(final_data)
            
        except Exception as e:
            logger.error(f"保存最终结果失败: {e}")
    
    def calculate_risk_type_stats(self, results):
        """计算各风险类型的统计信息"""
        risk_stats = {}
        
        for result in results:
            risk_type = result.get('risk_type', 'unknown')
            if risk_type not in risk_stats:
                risk_stats[risk_type] = {
                    'total': 0,
                    'successful': 0,
                    'failed': 0,
                    'upload_success': 0,
                    'saved_files': 0
                }
            
            risk_stats[risk_type]['total'] += 1
            
            if result.get('test_success'):
                risk_stats[risk_type]['successful'] += 1
            else:
                risk_stats[risk_type]['failed'] += 1
            
            if result.get('upload_success'):
                risk_stats[risk_type]['upload_success'] += 1
            
            if result.get('saved_file'):
                risk_stats[risk_type]['saved_files'] += 1
        
        # 计算成功率
        for risk_type, stats in risk_stats.items():
            if stats['total'] > 0:
                stats['success_rate'] = stats['successful'] / stats['total'] * 100
            else:
                stats['success_rate'] = 0
        
        return risk_stats
    
    async def generate_summary_report(self, final_data):
        """生成简要报告"""
        try:
            summary = final_data['test_summary']
            risk_stats = final_data['risk_type_stats']
            
            report = f"""
=== ChatGPT批量安全测试报告 ===
测试时间: {summary['start_time']} - {summary['end_time']}
测试时长: {summary['test_duration_formatted']}

=== 总体统计 ===
总测试数: {summary['total_tests']}
成功测试: {summary['successful_tests']}
失败测试: {summary['failed_tests']}
总体成功率: {summary['success_rate']:.2f}%
文件上传成功: {summary['upload_success_count']}
保存回复文件: {summary['saved_files_count']}

=== 各风险类型统计 ===
"""
            
            for risk_type, stats in risk_stats.items():
                report += f"""
{risk_type}:
  测试数量: {stats['total']}
  成功: {stats['successful']} ({stats['success_rate']:.1f}%)
  失败: {stats['failed']}
  文件上传成功: {stats['upload_success']}
  保存文件: {stats['saved_files']}
"""
            
            # 保存报告
            report_filename = f"test_summary_report_{int(self.test_stats['start_time'])}.txt"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"📋 测试报告已生成: {report_filename}")
            
            # 打印到控制台
            print(report)
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")

async def main():
    """主函数"""
    print("🚀 ChatGPT批量安全测试")
    
    tester = BatchChatGPTTester()
    
    try:
        # 连接浏览器
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            print("请先运行: python start_chrome_debug.py")
            return
        
        # 等待页面准备就绪
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        # 动态查找选择器
        await tester.find_and_update_selectors()
        
        print("✅ 已连接到ChatGPT页面")
        
        # 选择测试规模
        print("\n请选择批量测试规模:")
        print("1. 小规模测试 (每个风险类型2个用例)")
        print("2. 中等规模测试 (每个风险类型10个用例)")
        print("3. 大规模测试 (每个风险类型50个用例)")
        print("4. 完整测试 (所有880个用例)")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        test_configs = {
            "1": {"max_tests_per_type": 2, "interval": 3},
            "2": {"max_tests_per_type": 10, "interval": 4},
            "3": {"max_tests_per_type": 50, "interval": 5},
            "4": {"max_tests_per_type": None, "interval": 5}
        }
        
        if choice in test_configs:
            config = test_configs[choice]
            
            print(f"\n开始批量测试...")
            print(f"每个风险类型最大测试数: {config['max_tests_per_type'] or '全部'}")
            print(f"测试间隔: {config['interval']}秒")
            
            # 运行批量测试
            results = await tester.run_full_batch_tests(
                max_tests_per_type=config['max_tests_per_type'],
                test_interval=config['interval']
            )
            
            print(f"\n🎉 批量测试完成!")
            print(f"总共测试了 {len(results)} 个用例")
            
        else:
            print("无效选择")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
