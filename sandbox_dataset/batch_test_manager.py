#!/usr/bin/env python3
"""
批量测试管理器
提供批量测试的配置、监控和恢复功能
"""

import json
import time
import asyncio
from pathlib import Path
from batch_test_chatgpt import BatchChatGPTTester

class BatchTestManager:
    """批量测试管理器"""
    
    def __init__(self):
        self.config_file = "batch_test_config.json"
        self.progress_file = "batch_test_progress.json"
        
    def create_test_config(self):
        """创建测试配置"""
        config = {
            "test_profiles": {
                "quick": {
                    "name": "快速测试",
                    "max_tests_per_type": 2,
                    "test_interval": 3,
                    "description": "每个风险类型2个用例，适合快速验证"
                },
                "standard": {
                    "name": "标准测试", 
                    "max_tests_per_type": 10,
                    "test_interval": 4,
                    "description": "每个风险类型10个用例，平衡速度和覆盖率"
                },
                "comprehensive": {
                    "name": "全面测试",
                    "max_tests_per_type": 50,
                    "test_interval": 5,
                    "description": "每个风险类型50个用例，高覆盖率测试"
                },
                "complete": {
                    "name": "完整测试",
                    "max_tests_per_type": None,
                    "test_interval": 5,
                    "description": "所有880个用例，完整安全评估"
                }
            },
            "risk_type_priorities": [
                "network",
                "sensitive_info", 
                "privilege_escalation",
                "escape",
                "muti_user_isolation",
                "resource_exhaustion"
            ],
            "safety_settings": {
                "max_consecutive_failures": 10,
                "auto_pause_on_errors": True,
                "save_progress_interval": 10,
                "max_test_duration_hours": 8
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试配置已创建: {self.config_file}")
        return config
    
    def load_config(self):
        """加载测试配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print("配置文件不存在，创建默认配置...")
            return self.create_test_config()
    
    def show_test_profiles(self):
        """显示测试配置文件"""
        config = self.load_config()
        profiles = config['test_profiles']
        
        print("\n📋 可用的测试配置:")
        for key, profile in profiles.items():
            max_tests = profile['max_tests_per_type'] or "全部"
            print(f"  {key}: {profile['name']}")
            print(f"    - 每类测试数: {max_tests}")
            print(f"    - 测试间隔: {profile['test_interval']}秒")
            print(f"    - 说明: {profile['description']}")
            print()
    
    def estimate_test_time(self, profile_key):
        """估算测试时间"""
        config = self.load_config()
        profile = config['test_profiles'][profile_key]
        
        # 统计测试用例数量
        data_path = Path("data_folder")
        total_tests = 0
        
        if data_path.exists():
            for risk_type_folder in data_path.iterdir():
                if risk_type_folder.is_dir():
                    test_cases = len(list(risk_type_folder.iterdir()))
                    if profile['max_tests_per_type']:
                        test_cases = min(test_cases, profile['max_tests_per_type'])
                    total_tests += test_cases
        
        # 估算时间
        avg_test_time = 30  # 平均每个测试30秒
        interval_time = profile['test_interval']
        total_time = total_tests * (avg_test_time + interval_time)
        
        hours = total_time // 3600
        minutes = (total_time % 3600) // 60
        
        return {
            'total_tests': total_tests,
            'estimated_time_seconds': total_time,
            'estimated_time_formatted': f"{hours}小时{minutes}分钟"
        }
    
    def save_progress(self, current_test, total_tests, results):
        """保存测试进度"""
        progress = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'current_test': current_test,
            'total_tests': total_tests,
            'progress_percentage': current_test / total_tests * 100,
            'results_count': len(results),
            'last_test_result': results[-1] if results else None
        }
        
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress, f, ensure_ascii=False, indent=2)
    
    def load_progress(self):
        """加载测试进度"""
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return None
    
    def check_existing_results(self):
        """检查现有的测试结果"""
        data_path = Path("data_folder")
        result_files = []
        
        if data_path.exists():
            for result_file in data_path.rglob("chatgpt_result.txt"):
                result_files.append(result_file)
        
        return result_files
    
    def generate_test_plan(self, profile_key):
        """生成测试计划"""
        config = self.load_config()
        profile = config['test_profiles'][profile_key]
        estimate = self.estimate_test_time(profile_key)
        
        plan = f"""
=== ChatGPT批量测试计划 ===
配置: {profile['name']}
描述: {profile['description']}

=== 测试参数 ===
每个风险类型最大测试数: {profile['max_tests_per_type'] or '全部'}
测试间隔: {profile['test_interval']}秒
预计总测试数: {estimate['total_tests']}
预计耗时: {estimate['estimated_time_formatted']}

=== 风险类型优先级 ===
"""
        
        priorities = config['risk_type_priorities']
        for i, risk_type in enumerate(priorities, 1):
            plan += f"{i}. {risk_type}\n"
        
        plan += f"""
=== 安全设置 ===
最大连续失败数: {config['safety_settings']['max_consecutive_failures']}
错误时自动暂停: {config['safety_settings']['auto_pause_on_errors']}
进度保存间隔: {config['safety_settings']['save_progress_interval']}个测试
最大测试时长: {config['safety_settings']['max_test_duration_hours']}小时
"""
        
        return plan

async def interactive_batch_test():
    """交互式批量测试"""
    manager = BatchTestManager()
    
    print("🚀 ChatGPT批量测试管理器")
    
    # 显示测试配置
    manager.show_test_profiles()
    
    # 选择测试配置
    profile_key = input("请选择测试配置 (quick/standard/comprehensive/complete): ").strip()
    
    if profile_key not in ['quick', 'standard', 'comprehensive', 'complete']:
        print("❌ 无效的配置选择")
        return
    
    # 显示测试计划
    plan = manager.generate_test_plan(profile_key)
    print(plan)
    
    # 确认执行
    confirm = input("\n是否执行此测试计划? (y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    # 检查现有结果
    existing_results = manager.check_existing_results()
    if existing_results:
        print(f"\n⚠️ 发现 {len(existing_results)} 个现有测试结果文件")
        overwrite = input("是否覆盖现有结果? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("测试已取消")
            return
    
    # 开始测试
    config = manager.load_config()
    profile = config['test_profiles'][profile_key]
    
    tester = BatchChatGPTTester()
    
    try:
        # 连接浏览器
        print("\n🔗 连接Chrome浏览器...")
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            print("请先运行: python start_chrome_debug.py")
            return
        
        # 等待页面准备就绪
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        await tester.find_and_update_selectors()
        print("✅ 已连接到ChatGPT页面")
        
        # 执行批量测试
        print(f"\n🧪 开始执行 {profile['name']}...")
        results = await tester.run_full_batch_tests(
            max_tests_per_type=profile['max_tests_per_type'],
            test_interval=profile['test_interval']
        )
        
        print(f"\n🎉 批量测试完成!")
        print(f"总共完成 {len(results)} 个测试")
        
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        await tester.close()

def main():
    """主函数"""
    print("🎛️ ChatGPT批量测试管理器")
    
    while True:
        print("\n请选择操作:")
        print("1. 查看测试配置")
        print("2. 开始交互式批量测试")
        print("3. 检查现有测试结果")
        print("4. 生成测试计划")
        print("5. 退出")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            manager = BatchTestManager()
            manager.show_test_profiles()
            
        elif choice == "2":
            asyncio.run(interactive_batch_test())
            
        elif choice == "3":
            manager = BatchTestManager()
            results = manager.check_existing_results()
            print(f"\n📊 找到 {len(results)} 个现有测试结果文件")
            if results:
                for i, result_file in enumerate(results[:10], 1):
                    print(f"  {i}. {result_file}")
                if len(results) > 10:
                    print(f"  ... 还有 {len(results)-10} 个文件")
            
        elif choice == "4":
            manager = BatchTestManager()
            profile_key = input("请输入配置名称 (quick/standard/comprehensive/complete): ").strip()
            if profile_key in ['quick', 'standard', 'comprehensive', 'complete']:
                plan = manager.generate_test_plan(profile_key)
                print(plan)
            else:
                print("❌ 无效的配置名称")
                
        elif choice == "5":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
