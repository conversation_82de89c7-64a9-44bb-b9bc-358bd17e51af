# Copyright (c) Microsoft Corporation.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

StackFrame:
  type: object
  properties:
    file: string
    line: number
    column: number
    function: string?

# This object can be send with any rpc call in the "metadata" field.

Metadata:
  type: object
  properties:
    location:
      type: object?
      properties:
        file: string
        line: number?
        column: number?
    apiName: string?
    internal: boolean?
    # Test runner step id.
    stepId: string?

ClientSideCallMetadata:
  type: object
  properties:
    id: number
    stack:
      type: array?
      items: StackFrame

Point:
  type: object
  properties:
    x: number
    y: number


Rect:
  type: object
  properties:
    x: number
    y: number
    width: number
    height: number


SerializedValue:
  type: object
  # Exactly one of the properties must be present.
  properties:
    n: number?
    b: boolean?
    s: string?
    v:
      type: enum?
      literals:
      - null
      - undefined
      - NaN
      - Infinity
      - -Infinity
      - "-0"
    # String representation of the Date.
    d: string?
    # String representation of the URL.
    u: string?
    # String representation of BigInt.
    bi: string?
    # Typed array.
    ta:
      type: object?
      properties:
        b: binary
        k:
          type: enum
          literals:
          - i8
          - ui8
          - ui8c
          - i16
          - ui16
          - i32
          - ui32
          - f32
          - f64
          - bi64
          - bui64
    # Serialized Error object.
    e:
      type: object?
      properties:
        m: string
        n: string
        s: string
    # Regular expression pattern and flags.
    r:
      type: object?
      properties:
        p: string
        f: string
    a:
      type: array?
      items: SerializedValue
    # Object with keys and values.
    o:
      type: array?
      items:
        type: object
        properties:
          k: string
          v: SerializedValue
    # An index in the handles array from SerializedArgument.
    h: number?
    # Index of the object in value-type for circular reference resolution.
    id: number?
    # Ref to the object in value-type for circular reference resolution.
    ref: number?


# Represents a value with handle references.
SerializedArgument:
  type: object
  properties:
    value: SerializedValue
    handles:
      type: array
      items: Channel


ExpectedTextValue:
  type: object
  properties:
    string: string?
    regexSource: string?
    regexFlags: string?
    matchSubstring: boolean?
    ignoreCase: boolean?
    normalizeWhiteSpace: boolean?


AXNode:
  type: object
  properties:
    role: string
    name: string
    valueString: string?
    valueNumber: number?
    description: string?
    keyshortcuts: string?
    roledescription: string?
    valuetext: string?
    disabled: boolean?
    expanded: boolean?
    focused: boolean?
    modal: boolean?
    multiline: boolean?
    multiselectable: boolean?
    readonly: boolean?
    required: boolean?
    selected: boolean?
    checked:
      type: enum?
      literals:
      - checked
      - unchecked
      - mixed
    pressed:
      type: enum?
      literals:
      - pressed
      - released
      - mixed
    level: number?
    valuemin: number?
    valuemax: number?
    autocomplete: string?
    haspopup: string?
    invalid: string?
    orientation: string?
    children:
      type: array?
      items: AXNode


SetNetworkCookie:
  type: object
  properties:
    name: string
    value: string
    url: string?
    domain: string?
    path: string?
    expires: number?
    httpOnly: boolean?
    secure: boolean?
    sameSite:
      type: enum?
      literals:
      - Strict
      - Lax
      - None


NetworkCookie:
  type: object
  properties:
    name: string
    value: string
    domain: string
    path: string
    expires: number
    httpOnly: boolean
    secure: boolean
    sameSite:
      type: enum
      literals:
      - Strict
      - Lax
      - None


NameValue:
  type: object
  properties:
    name: string
    value: string

IndexedDBDatabase:
  type: object
  properties:
    name: string
    version: number
    stores:
      type: array
      items:
        type: object
        properties:
          name: string
          autoIncrement: boolean
          keyPath: string?
          keyPathArray:
            type: array?
            items: string
          records:
            type: array
            items:
              type: object
              properties:
                key: json?
                keyEncoded: json?
                value: json?
                valueEncoded: json?
          indexes:
            type: array
            items:
              type: object
              properties:
                name: string
                keyPath: string?
                keyPathArray:
                  type: array?
                  items: string
                multiEntry: boolean
                unique: boolean

SetOriginStorage:
  type: object
  properties:
    origin: string
    localStorage:
      type: array
      items: NameValue
    indexedDB:
      type: array?
      items: IndexedDBDatabase

OriginStorage:
  type: object
  properties:
    origin: string
    localStorage:
      type: array
      items: NameValue
    indexedDB:
      type: array?
      items: IndexedDBDatabase

SerializedError:
  type: object
  properties:
    error:
      type: object?
      properties:
        message: string
        name: string
        stack: string?
    value: SerializedValue?


RecordHarOptions:
  type: object
  properties:
    path: string
    content:
      type: enum?
      literals:
      - embed
      - attach
      - omit
    mode:
      type: enum?
      literals:
      - full
      - minimal
    urlGlob: string?
    urlRegexSource: string?
    urlRegexFlags: string?


FormField:
  type: object
  properties:
    name: string
    value: string?
    file:
      type: object?
      properties:
        name: string
        mimeType: string?
        buffer: binary

APIRequestContext:
  type: interface

  initializer:
    tracing: Tracing

  commands:

    fetch:
      parameters:
        url: string
        encodedParams: string?
        params:
          type: array?
          items: NameValue
        method: string?
        headers:
          type: array?
          items: NameValue
        postData: binary?
        jsonData: string?
        formData:
          type: array?
          items: NameValue
        multipartData:
          type: array?
          items: FormField
        timeout: number?
        failOnStatusCode: boolean?
        ignoreHTTPSErrors: boolean?
        maxRedirects: number?
        maxRetries: number?
      returns:
        response: APIResponse

    fetchResponseBody:
      parameters:
        fetchUid: string
      returns:
        binary: binary?

    fetchLog:
      parameters:
        fetchUid: string
      returns:
        log:
          type: array
          items: string

    storageState:
      parameters:
        indexedDB: boolean?
      returns:
        cookies:
          type: array
          items: NetworkCookie
        origins:
          type: array
          items: OriginStorage

    disposeAPIResponse:
      parameters:
        fetchUid: string

    dispose:
      parameters:
        reason: string?


APIResponse:
  type: object
  properties:
    fetchUid: string
    url: string
    status: number
    statusText: string
    headers:
      type: array
      items: NameValue


LifecycleEvent:
  type: enum
  literals:
  - load
  - domcontentloaded
  - networkidle
  - commit

CommonScreenshotOptions:
  type: mixin
  properties:
    omitBackground: boolean?
    caret:
      type: enum?
      literals:
      - hide
      - initial
    animations:
      type: enum?
      literals:
      - disabled
      - allow
    scale:
      type: enum?
      literals:
      - css
      - device
    mask:
      type: array?
      items:
        type: object
        properties:
          frame: Frame
          selector: string
    maskColor: string?
    style: string?

LaunchOptions:
  type: mixin
  properties:
    channel: string?
    executablePath: string?
    args:
      type: array?
      items: string
    ignoreAllDefaultArgs: boolean?
    ignoreDefaultArgs:
      type: array?
      items: string
    handleSIGINT: boolean?
    handleSIGTERM: boolean?
    handleSIGHUP: boolean?
    timeout: number?
    env:
      type: array?
      items: NameValue
    headless: boolean?
    devtools: boolean?
    proxy:
      type: object?
      properties:
        server: string
        bypass: string?
        username: string?
        password: string?
    downloadsPath: string?
    tracesDir: string?
    chromiumSandbox: boolean?
    firefoxUserPrefs: json?


ContextOptions:
  type: mixin
  properties:
    noDefaultViewport: boolean?
    viewport:
      type: object?
      properties:
        width: number
        height: number
    screen:
      type: object?
      properties:
        width: number
        height: number
    ignoreHTTPSErrors: boolean?
    clientCertificates:
      type: array?
      items:
        type: object
        properties:
          origin: string
          cert: binary?
          key: binary?
          passphrase: string?
          pfx: binary?
    javaScriptEnabled: boolean?
    bypassCSP: boolean?
    userAgent: string?
    locale: string?
    timezoneId: string?
    geolocation:
      type: object?
      properties:
        longitude: number
        latitude: number
        accuracy: number?
    permissions:
      type: array?
      items: string
    extraHTTPHeaders:
      type: array?
      items: NameValue
    offline: boolean?
    httpCredentials:
      type: object?
      properties:
        username: string
        password: string
        origin: string?
        send:
          type: enum?
          literals:
          - always
          - unauthorized
    deviceScaleFactor: number?
    isMobile: boolean?
    hasTouch: boolean?
    colorScheme:
      type: enum?
      literals:
      - dark
      - light
      - no-preference
      - no-override
    reducedMotion:
      type: enum?
      literals:
      - reduce
      - no-preference
      - no-override
    forcedColors:
      type: enum?
      literals:
      - active
      - none
      - no-override
    acceptDownloads:
      type: enum?
      literals:
      - accept
      - deny
      - internal-browser-default
    contrast:
      type: enum?
      literals:
      - no-preference
      - more
      - no-override
    baseURL: string?
    recordVideo:
      type: object?
      properties:
        dir: string
        size:
          type: object?
          properties:
            width: number
            height: number
    recordHar: RecordHarOptions?
    strictSelectors: boolean?
    serviceWorkers:
      type: enum?
      literals:
      - allow
      - block

LocalUtils:
  type: interface

  initializer:
    deviceDescriptors:
      type: array
      items:
        type: object
        properties:
          name: string
          descriptor:
            type: object
            properties:
              userAgent: string
              viewport:
                type: object
                properties:
                  width: number
                  height: number
              screen:
                type: object?
                properties:
                  width: number
                  height: number
              deviceScaleFactor: number
              isMobile: boolean
              hasTouch: boolean
              defaultBrowserType:
                type: enum
                literals:
                - chromium
                - firefox
                - webkit

  commands:

    zip:
      parameters:
        zipFile: string
        entries:
          type: array
          items: NameValue
        stacksId: string?
        mode:
          type: enum
          literals:
            - write
            - append
        includeSources: boolean

    harOpen:
      parameters:
        file: string
      returns:
        harId: string?
        error: string?

    harLookup:
      parameters:
        harId: string
        url: string
        method: string
        headers:
          type: array
          items: NameValue
        postData: binary?
        isNavigationRequest: boolean
      returns:
        action:
          type: enum
          literals:
          - error
          - redirect
          - fulfill
          - noentry
        message: string?
        redirectURL: string?
        status: number?
        headers:
          type: array?
          items: NameValue
        body: binary?

    harClose:
      parameters:
        harId: string

    harUnzip:
      parameters:
        zipFile: string
        harFile: string

    connect:
      parameters:
        wsEndpoint: string
        headers: json?
        exposeNetwork: string?
        slowMo: number?
        timeout: number?
        socksProxyRedirectPortForTest: number?
      returns:
        pipe: JsonPipe
        headers:
          type: array
          items: NameValue

    tracingStarted:
      parameters:
        tracesDir: string?
        traceName: string
      returns:
        stacksId: string

    addStackToTracingNoReply:
      parameters:
        callData: ClientSideCallMetadata

    traceDiscarded:
      parameters:
        stacksId: string

    globToRegex:
      parameters:
        glob: string
        baseURL: string?
        webSocketUrl: boolean?
      returns:
        regex: string

Root:
  type: interface

  commands:

    initialize:
      parameters:
        sdkLanguage:
          type: enum
          literals:
          - javascript
          - python
          - java
          - csharp
      returns:
        playwright: Playwright

Playwright:
  type: interface

  initializer:
    chromium: BrowserType
    firefox: BrowserType
    webkit: BrowserType
    bidiChromium: BrowserType
    bidiFirefox: BrowserType
    android: Android
    electron: Electron
    utils: LocalUtils?
    selectors: Selectors
    # Only present when connecting remotely via BrowserType.connect() method.
    preLaunchedBrowser: Browser?
    # Only present when connecting remotely via Android.connect() method.
    preConnectedAndroidDevice: AndroidDevice?
    # Only present when socks proxy is supported.
    socksSupport: SocksSupport?

  commands:
    newRequest:
      parameters:
        baseURL: string?
        userAgent: string?
        ignoreHTTPSErrors: boolean?
        extraHTTPHeaders:
          type: array?
          items: NameValue
        failOnStatusCode: boolean?
        clientCertificates:
          type: array?
          items:
            type: object
            properties:
              origin: string
              cert: binary?
              key: binary?
              passphrase: string?
              pfx: binary?
        maxRedirects: number?
        httpCredentials:
          type: object?
          properties:
            username: string
            password: string
            origin: string?
            send:
              type: enum?
              literals:
              - always
              - unauthorized
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?
        timeout: number?
        storageState:
          type: object?
          properties:
            cookies:
              type: array?
              items: NetworkCookie
            origins:
              type: array?
              items: SetOriginStorage
        tracesDir: string?

      returns:
        request: APIRequestContext

RecorderSource:
  type: object
  properties:
    isRecorded: boolean
    id: string
    label: string
    text: string
    language: string
    highlight:
      type: array
      items:
        type: object
        properties:
          line: number
          type: string
    revealLine: number?
    group: string?

DebugController:
  type: interface

  commands:
    initialize:
      parameters:
        codegenId: string
        sdkLanguage:
          type: enum
          literals:
          - javascript
          - python
          - java
          - csharp

    setReportStateChanged:
      parameters:
        enabled: boolean

    resetForReuse:

    navigate:
      parameters:
        url: string

    setRecorderMode:
      parameters:
        mode:
          type: enum
          literals:
            - inspecting
            - recording
            - none
        testIdAttributeName: string?

    highlight:
      parameters:
        selector: string?
        ariaTemplate: string?

    hideHighlight:

    resume:

    kill:

    closeAllBrowsers:

  events:
    inspectRequested:
      parameters:
        selector: string
        locator: string
        ariaSnapshot: string

    setModeRequested:
      parameters:
        mode: string

    stateChanged:
      parameters:
        pageCount: number

    sourceChanged:
      parameters:
        text: string
        header: string?
        footer: string?
        actions:
          type: array?
          items: string

    paused:
      parameters:
        paused: boolean

SocksSupport:
  type: interface

  commands:
    socksConnected:
      parameters:
        uid: string
        host: string
        port: number

    socksFailed:
      parameters:
        uid: string
        errorCode: string

    socksData:
      parameters:
        uid: string
        data: binary

    socksError:
      parameters:
        uid: string
        error: string

    socksEnd:
      parameters:
        uid: string

  events:
    socksRequested:
      parameters:
        uid: string
        host: string
        port: number

    socksData:
      parameters:
        uid: string
        data: binary

    socksClosed:
      parameters:
        uid: string

Selectors:
  type: interface

  commands:

    register:
      parameters:
        name: string
        source: string
        contentScript: boolean?

    setTestIdAttributeName:
      parameters:
        testIdAttributeName: string

BrowserType:
  type: interface

  initializer:
    executablePath: string
    name: string

  commands:

    launch:
      parameters:
        $mixin: LaunchOptions
        slowMo: number?
      returns:
        browser: Browser

    launchPersistentContext:
      parameters:
        $mixin1: LaunchOptions
        $mixin2: ContextOptions
        userDataDir: string
        slowMo: number?
      returns:
        context: BrowserContext

    connectOverCDP:
      parameters:
        endpointURL: string
        headers:
          type: array?
          items: NameValue
        slowMo: number?
        timeout: number?
      returns:
        browser: Browser
        defaultContext: BrowserContext?

Browser:
  type: interface

  initializer:
    version: string
    name: string

  commands:

    close:
      parameters:
        reason: string?

    killForTests:

    defaultUserAgentForTest:
      returns:
        userAgent: string

    newContext:
      parameters:
        $mixin: ContextOptions
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?
        storageState:
          type: object?
          properties:
            cookies:
              type: array?
              items: SetNetworkCookie
            origins:
              type: array?
              items: SetOriginStorage
      returns:
        context: BrowserContext

    newContextForReuse:
      parameters:
        $mixin: ContextOptions
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?
        storageState:
          type: object?
          properties:
            cookies:
              type: array?
              items: SetNetworkCookie
            origins:
              type: array?
              items: SetOriginStorage
      returns:
        context: BrowserContext

    stopPendingOperations:
      parameters:
        reason: string

    newBrowserCDPSession:
      returns:
        session: CDPSession

    startTracing:
      parameters:
        page: Page?
        screenshots: boolean?
        categories:
          type: array?
          items: string

    stopTracing:
      returns:
        artifact: Artifact


  events:

    close:

ConsoleMessage:
  type: mixin
  properties:
    type: string
    text: string
    args:
      type: array
      items: JSHandle
    location:
      type: object
      properties:
        url: string
        lineNumber: number
        columnNumber: number


EventTarget:
  type: interface

  commands:
    waitForEventInfo:
      parameters:
        info:
          type: object
          properties:
            waitId: string
            phase:
              type: enum
              literals:
              - before
              - after
              - log
            event: string?
            message: string?
            error: string?
      flags:
        snapshot: true

BrowserContext:
  type: interface

  extends: EventTarget

  initializer:
    isChromium: boolean
    requestContext: APIRequestContext
    tracing: Tracing

  commands:

    addCookies:
      parameters:
        cookies:
          type: array
          items: SetNetworkCookie

    addInitScript:
      parameters:
        source: string

    clearCookies:
      parameters:
        name: string?
        nameRegexSource: string?
        nameRegexFlags: string?
        domain: string?
        domainRegexSource: string?
        domainRegexFlags: string?
        path: string?
        pathRegexSource: string?
        pathRegexFlags: string?

    clearPermissions:

    close:
      parameters:
        reason: string?

    cookies:
      parameters:
        urls:
          type: array
          items: string
      returns:
        cookies:
          type: array
          items: NetworkCookie

    exposeBinding:
      parameters:
        name: string
        needsHandle: boolean?

    grantPermissions:
      parameters:
        permissions:
          type: array
          items: string
        origin: string?

    newPage:
      returns:
        page: Page

    setDefaultNavigationTimeoutNoReply:
      parameters:
        timeout: number?

    setDefaultTimeoutNoReply:
      parameters:
        timeout: number?

    setExtraHTTPHeaders:
      parameters:
        headers:
          type: array
          items: NameValue

    setGeolocation:
      parameters:
        geolocation:
          type: object?
          properties:
            longitude: number
            latitude: number
            accuracy: number?

    setHTTPCredentials:
      parameters:
        httpCredentials:
          type: object?
          properties:
            username: string
            password: string
            origin: string?

    setNetworkInterceptionPatterns:
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setWebSocketInterceptionPatterns:
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setOffline:
      parameters:
        offline: boolean

    storageState:
      parameters:
        indexedDB: boolean?
      returns:
        cookies:
          type: array
          items: NetworkCookie
        origins:
          type: array
          items: OriginStorage

    pause:
      experimental: True

    enableRecorder:
      experimental: True
      parameters:
        language: string?
        mode:
          type: enum?
          literals:
          - inspecting
          - recording
        pauseOnNextStatement: boolean?
        testIdAttributeName: string?
        launchOptions: json?
        contextOptions: json?
        device: string?
        saveStorage: string?
        outputFile: string?
        handleSIGINT: boolean?
        omitCallTracking: boolean?

    newCDPSession:
      parameters:
        page: Page?
        frame: Frame?
      returns:
        session: CDPSession

    harStart:
      parameters:
        page: Page?
        options: RecordHarOptions
      returns:
        harId: string

    harExport:
      parameters:
        harId: string?
      returns:
        artifact: Artifact

    createTempFiles:
      parameters:
        rootDirName: string?
        items:
          type: array
          items:
            type: object
            properties:
              name: string
              lastModifiedMs: number?
      returns:
        rootDir: WritableStream?
        writableStreams:
          type: array
          items: WritableStream

    updateSubscription:
      parameters:
        event:
          type: enum
          literals:
          - console
          - dialog
          - request
          - response
          - requestFinished
          - requestFailed
        enabled: boolean

    clockFastForward:
      parameters:
        ticksNumber: number?
        ticksString: string?

    clockInstall:
      parameters:
        timeNumber: number?
        timeString: string?

    clockPauseAt:
      parameters:
        timeNumber: number?
        timeString: string?

    clockResume:

    clockRunFor:
      parameters:
        ticksNumber: number?
        ticksString: string?

    clockSetFixedTime:
      parameters:
        timeNumber: number?
        timeString: string?

    clockSetSystemTime:
      parameters:
        timeNumber: number?
        timeString: string?

  events:

    bindingCall:
      parameters:
        binding: BindingCall

    console:
      parameters:
        $mixin: ConsoleMessage
        page: Page

    close:

    dialog:
      parameters:
        dialog: Dialog

    page:
      parameters:
        page: Page

    pageError:
      parameters:
        error: SerializedError
        page: Page

    route:
      parameters:
        route: Route

    webSocketRoute:
      parameters:
        webSocketRoute: WebSocketRoute

    video:
      parameters:
        artifact: Artifact

    backgroundPage:
      parameters:
        page: Page

    serviceWorker:
      parameters:
        worker: Worker

    request:
      parameters:
        request: Request
        page: Page?

    requestFailed:
      parameters:
        request: Request
        failureText: string?
        responseEndTiming: number
        page: Page?

    requestFinished:
      parameters:
        request: Request
        response: Response?
        responseEndTiming: number
        page: Page?

    response:
      parameters:
        response: Response
        page: Page?

Page:
  type: interface

  extends: EventTarget

  initializer:
    mainFrame: Frame
    viewportSize:
      type: object?
      properties:
        width: number
        height: number
    isClosed: boolean
    opener: Page?

  commands:

    setDefaultNavigationTimeoutNoReply:
      parameters:
        timeout: number?

    setDefaultTimeoutNoReply:
      parameters:
        timeout: number?

    addInitScript:
      parameters:
        source: string

    close:
      parameters:
        runBeforeUnload: boolean?
        reason: string?

    emulateMedia:
      parameters:
        media:
          type: enum?
          literals:
          - screen
          - print
          - no-override
        colorScheme:
          type: enum?
          literals:
          - dark
          - light
          - no-preference
          - no-override
        reducedMotion:
          type: enum?
          literals:
          - reduce
          - no-preference
          - no-override
        forcedColors:
          type: enum?
          literals:
          - active
          - none
          - no-override
        contrast:
          type: enum?
          literals:
          - no-preference
          - more
          - no-override
      flags:
        snapshot: true

    exposeBinding:
      parameters:
        name: string
        needsHandle: boolean?

    goBack:
      parameters:
        timeout: number?
        waitUntil: LifecycleEvent?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    goForward:
      parameters:
        timeout: number?
        waitUntil: LifecycleEvent?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    requestGC:

    registerLocatorHandler:
      parameters:
        selector: string
        noWaitAfter: boolean?
      returns:
        uid: number

    resolveLocatorHandlerNoReply:
      parameters:
        uid: number
        remove: boolean?

    unregisterLocatorHandler:
      parameters:
        uid: number

    reload:
      parameters:
        timeout: number?
        waitUntil: LifecycleEvent?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    expectScreenshot:
      parameters:
        expected: binary?
        timeout: number
        isNot: boolean
        locator:
          type: object?
          properties:
            frame: Frame
            selector: string
        comparator: string?
        maxDiffPixels: number?
        maxDiffPixelRatio: number?
        threshold: number?
        fullPage: boolean?
        clip: Rect?
        $mixin: CommonScreenshotOptions
      returns:
        diff: binary?
        errorMessage: string?
        actual: binary?
        previous: binary?
        timedOut: boolean?
        log:
          type: array?
          items: string
      flags:
        snapshot: true

    screenshot:
      parameters:
        timeout: number?
        type:
          type: enum?
          literals:
          - png
          - jpeg
        quality: number?
        fullPage: boolean?
        clip: Rect?
        $mixin: CommonScreenshotOptions
      returns:
        binary: binary
      flags:
        snapshot: true

    setExtraHTTPHeaders:
      parameters:
        headers:
          type: array
          items: NameValue

    setNetworkInterceptionPatterns:
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setWebSocketInterceptionPatterns:
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setViewportSize:
      parameters:
        viewportSize:
          type: object
          properties:
            width: number
            height: number
      flags:
        snapshot: true

    keyboardDown:
      parameters:
        key: string
      flags:
        slowMo: true
        snapshot: true

    keyboardUp:
      parameters:
        key: string
      flags:
        slowMo: true
        snapshot: true

    keyboardInsertText:
      parameters:
        text: string
      flags:
        slowMo: true
        snapshot: true

    keyboardType:
      parameters:
        text: string
        delay: number?
      flags:
        slowMo: true
        snapshot: true

    keyboardPress:
      parameters:
        key: string
        delay: number?
      flags:
        slowMo: true
        snapshot: true

    mouseMove:
      parameters:
        x: number
        y: number
        steps: number?
      flags:
        slowMo: true
        snapshot: true

    mouseDown:
      parameters:
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
      flags:
        slowMo: true
        snapshot: true

    mouseUp:
      parameters:
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
      flags:
        slowMo: true
        snapshot: true

    mouseClick:
      parameters:
        x: number
        y: number
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
      flags:
        slowMo: true
        snapshot: true

    mouseWheel:
      parameters:
        deltaX: number
        deltaY: number
      flags:
        slowMo: true
        snapshot: true

    touchscreenTap:
      parameters:
        x: number
        y: number
      flags:
        slowMo: true
        snapshot: true

    accessibilitySnapshot:
      parameters:
        interestingOnly: boolean?
        root: ElementHandle?
      returns:
        rootAXNode: AXNode?
      flags:
        snapshot: true

    pdf:
      parameters:
        scale: number?
        displayHeaderFooter: boolean?
        headerTemplate: string?
        footerTemplate: string?
        printBackground: boolean?
        landscape: boolean?
        pageRanges: string?
        format: string?
        width: string?
        height: string?
        preferCSSPageSize: boolean?
        margin:
          type: object?
          properties:
            top: string?
            bottom: string?
            left: string?
            right: string?
        tagged: boolean?
        outline: boolean?
      returns:
        pdf: binary

    startJSCoverage:
      parameters:
        resetOnNavigation: boolean?
        reportAnonymousScripts: boolean?

    stopJSCoverage:
      returns:
        entries:
          type: array
          items:
            type: object
            properties:
              url: string
              scriptId: string
              source: string?
              functions:
                type: array
                items:
                  type: object
                  properties:
                    functionName: string
                    isBlockCoverage: boolean
                    ranges:
                      type: array
                      items:
                        type: object
                        properties:
                          startOffset: number
                          endOffset: number
                          count: number

    startCSSCoverage:
      parameters:
        resetOnNavigation: boolean?

    stopCSSCoverage:
      returns:
        entries:
          type: array
          items:
            type: object
            properties:
              url: string
              text: string?
              ranges:
                type: array
                items:
                  type: object
                  properties:
                    start: number
                    end: number

    bringToFront:

    updateSubscription:
      parameters:
        event:
          type: enum
          literals:
          - console
          - dialog
          - fileChooser
          - request
          - response
          - requestFinished
          - requestFailed
        enabled: boolean

  events:

    bindingCall:
      parameters:
        binding: BindingCall

    close:

    crash:

    download:
      parameters:
        url: string
        suggestedFilename: string
        artifact: Artifact

    fileChooser:
      parameters:
        element: ElementHandle
        isMultiple: boolean

    frameAttached:
      parameters:
        frame: Frame

    frameDetached:
      parameters:
        frame: Frame

    locatorHandlerTriggered:
      parameters:
        uid: number

    route:
      parameters:
        route: Route

    webSocketRoute:
      parameters:
        webSocketRoute: WebSocketRoute

    video:
      parameters:
        artifact: Artifact

    webSocket:
      parameters:
        webSocket: WebSocket

    worker:
      parameters:
        worker: Worker



Frame:
  type: interface

  initializer:
    url: string
    name: string
    parentFrame: Frame?
    loadStates:
      type: array
      items: LifecycleEvent

  commands:

    evalOnSelector:
      parameters:
        selector: string
        strict: boolean?
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evalOnSelectorAll:
      parameters:
        selector: string
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    addScriptTag:
      parameters:
        url: string?
        content: string?
        type: string?
      returns:
        element: ElementHandle
      flags:
        snapshot: true

    addStyleTag:
      parameters:
        url: string?
        content: string?
      returns:
        element: ElementHandle
      flags:
        snapshot: true

    ariaSnapshot:
      parameters:
        selector: string
        ref: boolean?
        mode:
          type: enum?
          literals:
          - raw
          - regex
        timeout: number?
      returns:
        snapshot: string
      flags:
        snapshot: true

    blur:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true

    check:
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    click:
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        noWaitAfter: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    content:
      returns:
        value: string
      flags:
        snapshot: true

    dragAndDrop:
      parameters:
        source: string
        target: string
        force: boolean?
        timeout: number?
        trial: boolean?
        sourcePosition: Point?
        targetPosition: Point?
        strict: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    dblclick:
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    dispatchEvent:
      parameters:
        selector: string
        strict: boolean?
        type: string
        eventInit: SerializedArgument
        timeout: number?
      flags:
        slowMo: true
        snapshot: true

    evaluateExpression:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evaluateExpressionHandle:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle
      flags:
        snapshot: true

    fill:
      parameters:
        selector: string
        strict: boolean?
        value: string
        force: boolean?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    focus:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true

    frameElement:
      returns:
        element: ElementHandle

    highlight:
      parameters:
        selector: string

    getAttribute:
      parameters:
        selector: string
        strict: boolean?
        name: string
        timeout: number?
      returns:
        value: string?
      flags:
        snapshot: true

    goto:
      parameters:
        url: string
        timeout: number?
        waitUntil: LifecycleEvent?
        referer: string?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    hover:
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    innerHTML:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: string
      flags:
        snapshot: true

    innerText:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: string
      flags:
        snapshot: true

    inputValue:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: string
      flags:
        snapshot: true

    isChecked:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: boolean
      flags:
        snapshot: true

    isDisabled:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: boolean
      flags:
        snapshot: true

    isEnabled:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: boolean
      flags:
        snapshot: true

    isHidden:
      parameters:
        selector: string
        strict: boolean?
      returns:
        value: boolean
      flags:
        snapshot: true

    isVisible:
      parameters:
        selector: string
        strict: boolean?
      returns:
        value: boolean
      flags:
        snapshot: true

    isEditable:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: boolean
      flags:
        snapshot: true

    press:
      parameters:
        selector: string
        strict: boolean?
        key: string
        delay: number?
        noWaitAfter: boolean?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    querySelector:
      parameters:
        selector: string
        strict: boolean?
      returns:
        element: ElementHandle?
      flags:
        snapshot: true

    querySelectorAll:
      parameters:
        selector: string
      returns:
        elements:
          type: array
          items: ElementHandle
      flags:
        snapshot: true

    queryCount:
      parameters:
        selector: string
      returns:
        value: number
      flags:
        snapshot: true

    selectOption:
      parameters:
        selector: string
        strict: boolean?
        elements:
          type: array?
          items: ElementHandle
        options:
          type: array?
          items:
            type: object
            properties:
              valueOrLabel: string?
              value: string?
              label: string?
              index: number?
        force: boolean?
        timeout: number?
      returns:
        values:
          type: array
          items: string
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    setContent:
      parameters:
        html: string
        timeout: number?
        waitUntil: LifecycleEvent?
      flags:
        snapshot: true

    setInputFiles:
      parameters:
        selector: string
        strict: boolean?
        # Only one of payloads, localPaths and streams should be present.
        payloads:
          type: array?
          items:
            type: object
            properties:
              name: string
              mimeType: string?
              buffer: binary
        localDirectory: string?
        directoryStream: WritableStream?
        localPaths:
          type: array?
          items: string
        streams:
          type: array?
          items: WritableStream
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    tap:
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    textContent:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
      returns:
        value: string?
      flags:
        snapshot: true

    title:
      returns:
        value: string

    type:
      parameters:
        selector: string
        strict: boolean?
        text: string
        delay: number?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    uncheck:
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    waitForTimeout:
      parameters:
        timeout: number
      flags:
        snapshot: true

    waitForFunction:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
        timeout: number?
        # When present, polls on interval. Otherwise, polls on raf.
        pollingInterval: number?
      returns:
        handle: JSHandle
      flags:
        snapshot: true

    waitForSelector:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
        state:
          type: enum?
          literals:
          - attached
          - detached
          - visible
          - hidden
        omitReturnValue: boolean?
      returns:
        element: ElementHandle?
      flags:
        snapshot: true

    expect:
      parameters:
        selector: string
        expression: string
        expressionArg: json?
        expectedText:
          type: array?
          items: ExpectedTextValue
        expectedNumber: number?
        expectedValue: SerializedArgument?
        useInnerText: boolean?
        isNot: boolean
        timeout: number
      returns:
        matches: boolean
        received: SerializedValue?
        timedOut: boolean?
        log:
          type: array?
          items: string
      flags:
        snapshot: true

  events:

    loadstate:
      parameters:
        add: LifecycleEvent?
        remove: LifecycleEvent?

    navigated:
      parameters:
        url: string
        name: string
        newDocument:
          type: object?
          properties:
            request: Request?
        error: string?



Worker:
  type: interface

  initializer:
    url: string

  commands:

    evaluateExpression:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue

    evaluateExpressionHandle:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle

  events:

    close:


JSHandle:
  type: interface

  initializer:
    preview: string

  commands:

    dispose:

    evaluateExpression:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evaluateExpressionHandle:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle
      flags:
        snapshot: true

    getPropertyList:
      returns:
        properties:
          type: array
          items:
            type: object
            properties:
              name: string
              value: JSHandle

    getProperty:
      parameters:
        name: string
      returns:
        handle: JSHandle

    jsonValue:
      returns:
        value: SerializedValue

  events:

    previewUpdated:
      parameters:
        preview: string



ElementHandle:
  type: interface

  extends: JSHandle

  commands:

    evalOnSelector:
      parameters:
        selector: string
        strict: boolean?
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evalOnSelectorAll:
      parameters:
        selector: string
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    boundingBox:
      returns:
        value: Rect?
      flags:
        snapshot: true

    check:
      parameters:
        force: boolean?
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    click:
      parameters:
        force: boolean?
        noWaitAfter: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    contentFrame:
      returns:
        frame: Frame?
      flags:
        snapshot: true

    dblclick:
      parameters:
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    dispatchEvent:
      parameters:
        type: string
        eventInit: SerializedArgument
      flags:
        slowMo: true
        snapshot: true

    fill:
      parameters:
        value: string
        force: boolean?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    focus:
      flags:
        slowMo: true
        snapshot: true

    generateLocatorString:
      returns:
        value: string?

    getAttribute:
      parameters:
        name: string
      returns:
        value: string?

    hover:
      parameters:
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    innerHTML:
      returns:
        value: string
      flags:
        snapshot: true

    innerText:
      returns:
        value: string
      flags:
        snapshot: true

    inputValue:
      returns:
        value: string
      flags:
        snapshot: true

    isChecked:
      returns:
        value: boolean
      flags:
        snapshot: true

    isDisabled:
      returns:
        value: boolean
      flags:
        snapshot: true

    isEditable:
      returns:
        value: boolean
      flags:
        snapshot: true

    isEnabled:
      returns:
        value: boolean
      flags:
        snapshot: true

    isHidden:
      returns:
        value: boolean
      flags:
        snapshot: true

    isVisible:
      returns:
        value: boolean
      flags:
        snapshot: true

    ownerFrame:
      returns:
        frame: Frame?

    press:
      parameters:
        key: string
        delay: number?
        timeout: number?
        noWaitAfter: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    querySelector:
      parameters:
        selector: string
        strict: boolean?
      returns:
        element: ElementHandle?
      flags:
        snapshot: true

    querySelectorAll:
      parameters:
        selector: string
      returns:
        elements:
          type: array
          items: ElementHandle
      flags:
        snapshot: true

    screenshot:
      parameters:
        timeout: number?
        type:
          type: enum?
          literals:
          - png
          - jpeg
        quality: number?
        $mixin: CommonScreenshotOptions
      returns:
        binary: binary
      flags:
        snapshot: true

    scrollIntoViewIfNeeded:
      parameters:
        timeout: number?
      flags:
        slowMo: true
        snapshot: true

    selectOption:
      parameters:
        elements:
          type: array?
          items: ElementHandle
        options:
          type: array?
          items:
            type: object
            properties:
              valueOrLabel: string?
              value: string?
              label: string?
              index: number?
        force: boolean?
        timeout: number?
      returns:
        values:
          type: array
          items: string
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    selectText:
      parameters:
        force: boolean?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true

    setInputFiles:
      parameters:
        # Only one of payloads, localPaths and streams should be present.
        payloads:
          type: array?
          items:
            type: object
            properties:
              name: string
              mimeType: string?
              buffer: binary
        localDirectory: string?
        directoryStream: WritableStream?
        localPaths:
          type: array?
          items: string
        streams:
          type: array?
          items: WritableStream
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    tap:
      parameters:
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    textContent:
      returns:
        value: string?
      flags:
        snapshot: true

    type:
      parameters:
        text: string
        delay: number?
        timeout: number?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    uncheck:
      parameters:
        force: boolean?
        position: Point?
        timeout: number?
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    waitForElementState:
      parameters:
        state:
          type: enum
          literals:
          - visible
          - hidden
          - stable
          - enabled
          - disabled
          - editable
        timeout: number?
      flags:
        snapshot: true

    waitForSelector:
      parameters:
        selector: string
        strict: boolean?
        timeout: number?
        state:
          type: enum?
          literals:
          - attached
          - detached
          - visible
          - hidden
      returns:
        element: ElementHandle?
      flags:
        snapshot: true


Request:
  type: interface

  initializer:
    frame: Frame?
    serviceWorker: Worker?
    url: string
    resourceType: string
    method: string
    postData: binary?
    headers:
      type: array
      items: NameValue
    isNavigationRequest: boolean
    redirectedFrom: Request?

  commands:

    response:
      returns:
        response: Response?

    rawRequestHeaders:
      returns:
        headers:
          type: array
          items: NameValue


Route:
  type: interface

  initializer:
    request: Request

  commands:

    redirectNavigationRequest:
      parameters:
        url: string

    abort:
      parameters:
        errorCode: string?

    continue:
      parameters:
        url: string?
        method: string?
        headers:
          type: array?
          items: NameValue
        postData: binary?
        isFallback: boolean

    fulfill:
      parameters:
        # default is 200
        status: number?
        headers:
          type: array?
          items: NameValue
        body: string?
        isBase64: boolean?
        fetchResponseUid: string?


WebSocketRoute:
  type: interface

  initializer:
    url: string

  commands:

    connect:

    ensureOpened:

    sendToPage:
      parameters:
        message: string
        isBase64: boolean

    sendToServer:
      parameters:
        message: string
        isBase64: boolean

    closePage:
      parameters:
        code: number?
        reason: string?
        wasClean: boolean

    closeServer:
      parameters:
        code: number?
        reason: string?
        wasClean: boolean

  events:

    messageFromPage:
      parameters:
        message: string
        isBase64: boolean

    messageFromServer:
      parameters:
        message: string
        isBase64: boolean

    closePage:
      parameters:
        code: number?
        reason: string?
        wasClean: boolean

    closeServer:
      parameters:
        code: number?
        reason: string?
        wasClean: boolean


ResourceTiming:
  type: object
  properties:
    startTime: number
    domainLookupStart: number
    domainLookupEnd: number
    connectStart: number
    secureConnectionStart: number
    connectEnd: number
    requestStart: number
    responseStart: number

Response:
  type: interface

  initializer:
    request: Request
    url: string
    status: number
    statusText: string
    headers:
      type: array
      items: NameValue
    timing: ResourceTiming
    fromServiceWorker: boolean


  commands:

    body:
      returns:
        binary: binary

    securityDetails:
      returns:
        value: SecurityDetails?

    serverAddr:
      returns:
        value: RemoteAddr?

    rawResponseHeaders:
      returns:
        headers:
          type: array
          items: NameValue

    sizes:
      returns:
        sizes: RequestSizes


SecurityDetails:
  type: object
  properties:
    issuer: string?
    protocol: string?
    subjectName: string?
    validFrom: number?
    validTo: number?

RequestSizes:
  type: object
  properties:
    requestBodySize: number
    requestHeadersSize: number
    responseBodySize: number
    responseHeadersSize: number


RemoteAddr:
  type: object
  properties:
    ipAddress: string
    port: number


WebSocket:
  type: interface

  extends: EventTarget

  initializer:
    url: string

  events:
    open:

    frameSent:
      parameters:
        opcode: number
        data: string

    frameReceived:
      parameters:
        opcode: number
        data: string

    socketError:
      parameters:
        error: string

    close:


BindingCall:
  type: interface

  initializer:
    frame: Frame
    name: string
    args:
      type: array?
      items: SerializedValue
    handle: JSHandle?

  commands:

    reject:
      parameters:
        error: SerializedError

    resolve:
      parameters:
        result: SerializedArgument



Dialog:
  type: interface

  initializer:
    page: Page?
    type: string
    message: string
    defaultValue: string

  commands:

    accept:
      parameters:
        promptText: string?

    dismiss:


Tracing:
  type: interface

  commands:

    tracingStart:
      parameters:
        name: string?
        snapshots: boolean?
        screenshots: boolean?
        live: boolean?

    tracingStartChunk:
      parameters:
        name: string?
        title: string?
      returns:
        traceName: string

    tracingGroup:
      parameters:
        name: string
        location:
          type: object?
          properties:
            file: string
            line: number?
            column: number?

    tracingGroupEnd:

    tracingStopChunk:
      parameters:
        mode:
          type: enum
          literals:
          - archive
          - discard
          - entries
      returns:
        # The artifact may be missing if the browser closes while tracing is being stopped.
        # Or it can be missing if client-side compression is taking place.
        artifact: Artifact?
        # For local mode, these are all entries.
        entries:
          type: array?
          items: NameValue

    tracingStop:


Artifact:
  type: interface

  initializer:
    absolutePath: string

  commands:

    pathAfterFinished:
      returns:
        value: string

    # Blocks path/failure/delete/context.close until saved to the local |path|.
    saveAs:
      parameters:
        path: string

    # Blocks path/failure/delete/context.close until the stream is closed.
    saveAsStream:
      returns:
        stream: Stream

    failure:
      returns:
        error: string?

    stream:
      returns:
        stream: Stream

    cancel:

    delete:


Stream:
  type: interface

  commands:

    read:
      parameters:
        size: number?
      returns:
        binary: binary

    close:


WritableStream:
  type: interface

  commands:

    write:
      parameters:
        binary: binary

    close:


CDPSession:
  type: interface

  commands:

    send:
      parameters:
        method: string
        params: json?
      returns:
        result: json

    detach:

  events:

    event:
      parameters:
        method: string
        params: json?


Electron:
  type: interface

  commands:

    launch:
      parameters:
        executablePath: string?
        args:
          type: array?
          items: string
        cwd: string?
        env:
          type: array?
          items: NameValue
        timeout: number?
        acceptDownloads:
          type: enum?
          literals:
          - accept
          - deny
          - internal-browser-default
        bypassCSP: boolean?
        colorScheme:
          type: enum?
          literals:
          - dark
          - light
          - no-preference
          - no-override
        extraHTTPHeaders:
          type: array?
          items: NameValue
        geolocation:
          type: object?
          properties:
            longitude: number
            latitude: number
            accuracy: number?
        httpCredentials:
          type: object?
          properties:
            username: string
            password: string
            origin: string?
        ignoreHTTPSErrors: boolean?
        locale: string?
        offline: boolean?
        recordHar: RecordHarOptions?
        recordVideo:
          type: object?
          properties:
            dir: string
            size:
              type: object?
              properties:
                width: number
                height: number
        strictSelectors: boolean?
        timezoneId: string?
        tracesDir: string?

      returns:
        electronApplication: ElectronApplication


ElectronApplication:
  type: interface

  extends: EventTarget

  initializer:
    context: BrowserContext

  commands:

    browserWindow:
      parameters:
        page: Page
      returns:
        handle: JSHandle

    evaluateExpression:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue

    evaluateExpressionHandle:
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle

    updateSubscription:
      parameters:
        event:
          type: enum
          literals:
          - console
        enabled: boolean

  events:
    close:
    console:
      parameters:
        $mixin: ConsoleMessage

Android:
  type: interface

  commands:

    devices:
      parameters:
        host: string?
        port: number?
        omitDriverInstall: boolean?
      returns:
        devices:
          type: array
          items: AndroidDevice

    setDefaultTimeoutNoReply:
      parameters:
        timeout: number

AndroidSocket:
  type: interface

  commands:
    write:
      parameters:
        data: binary

    close:

  events:
    data:
      parameters:
        data: binary
    close:

AndroidDevice:
  type: interface

  extends: EventTarget

  initializer:
    model: string
    serial: string

  commands:
    wait:
      parameters:
        selector: AndroidSelector
        state:
          type: enum?
          literals:
          - gone
        timeout: number?

    fill:
      parameters:
        selector: AndroidSelector
        text: string
        timeout: number?

    tap:
      parameters:
        selector: AndroidSelector
        duration: number?
        timeout: number?

    drag:
      parameters:
        selector: AndroidSelector
        dest: Point
        speed: number?
        timeout: number?

    fling:
      parameters:
        selector: AndroidSelector
        direction:
          type: enum
          literals:
          - up
          - down
          - left
          - right
        speed: number?
        timeout: number?

    longTap:
      parameters:
        selector: AndroidSelector
        timeout: number?

    pinchClose:
      parameters:
        selector: AndroidSelector
        percent: number
        speed: number?
        timeout: number?

    pinchOpen:
      parameters:
        selector: AndroidSelector
        percent: number
        speed: number?
        timeout: number?

    scroll:
      parameters:
        selector: AndroidSelector
        direction:
          type: enum
          literals:
          - up
          - down
          - left
          - right
        percent: number
        speed: number?
        timeout: number?

    swipe:
      parameters:
        selector: AndroidSelector
        direction:
          type: enum
          literals:
          - up
          - down
          - left
          - right
        percent: number
        speed: number?
        timeout: number?

    info:
      parameters:
        selector: AndroidSelector
      returns:
        info: AndroidElementInfo

    screenshot:
      returns:
        binary: binary

    inputType:
      parameters:
        text: string

    inputPress:
      parameters:
        key: string

    inputTap:
      parameters:
        point: Point

    inputSwipe:
      parameters:
        segments:
          type: array
          items: Point
        steps: number

    inputDrag:
      parameters:
        from: Point
        to: Point
        steps: number

    launchBrowser:
      parameters:
        $mixin: ContextOptions
        pkg: string?
        args:
          type: array?
          items: string
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?

      returns:
        context: BrowserContext

    open:
      parameters:
        command: string
      returns:
        socket: AndroidSocket

    shell:
      parameters:
        command: string
      returns:
        result: binary

    installApk:
      parameters:
        file: binary
        args:
          type: array?
          items: string

    push:
      parameters:
        file: binary
        path: string
        mode: number?

    setDefaultTimeoutNoReply:
      parameters:
        timeout: number

    connectToWebView:
      parameters:
        socketName: string
      returns:
        context: BrowserContext

    close:

  events:
    close:

    webViewAdded:
      parameters:
        webView: AndroidWebView

    webViewRemoved:
      parameters:
        socketName: string


AndroidWebView:
  type: object
  properties:
    pid: number
    pkg: string
    socketName: string


AndroidSelector:
  type: object
  properties:
    checkable: boolean?
    checked: boolean?
    clazz: string?
    clickable: boolean?
    depth: number?
    desc: string?
    enabled: boolean?
    focusable: boolean?
    focused: boolean?
    hasChild:
      type: object?
      properties:
        selector: AndroidSelector
    hasDescendant:
      type: object?
      properties:
        selector: AndroidSelector
        maxDepth: number?
    longClickable: boolean?
    pkg: string?
    res: string?
    scrollable: boolean?
    selected: boolean?
    text: string?


AndroidElementInfo:
  type: object
  properties:
    children:
      type: array?
      items: AndroidElementInfo
    clazz: string
    desc: string
    res: string
    pkg: string
    text: string
    bounds: Rect
    checkable: boolean
    checked: boolean
    clickable: boolean
    enabled: boolean
    focusable: boolean
    focused: boolean
    longClickable: boolean
    scrollable: boolean
    selected: boolean


JsonPipe:
  type: interface

  commands:
    send:
      parameters:
        message: json

    close:

  events:

    message:
      parameters:
        message: json

    closed:
      parameters:
        reason: string?
