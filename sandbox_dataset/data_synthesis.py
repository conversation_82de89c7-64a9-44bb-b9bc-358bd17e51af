import json
import base64
from typing import List, Dict, Any


def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON文件并返回数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return []
    except json.JSONDecodeError as e:
        print(f"错误：解析JSON文件 {file_path} 失败: {e}")
        return []


def replace_instruction_placeholder(text: str, instruction: str) -> str:
    """替换文本中的{instruction}占位符"""
    return text.replace('{instruction}', instruction)


def encode_to_base64(text: str) -> str:
    """将文本编码为base64"""
    return base64.b64encode(text.encode('utf-8')).decode('utf-8')


def process_excel_template(excel_template: str, instruction: str, template_type: str) -> str:
    """处理excel_template，根据template_type决定是否进行base64编码"""
    # 首先替换占位符
    processed_template = replace_instruction_placeholder(excel_template, instruction)

    # 如果template_type是base64，则进行base64编码
    if template_type == "base64":
        return encode_to_base64(processed_template)
    else:
        return processed_template


def synthesize_data(instructions: List[Dict[str, Any]],
                   templates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """合成测试数据"""
    final_questions = []
    question_id = 1

    # 对每个instruction和每个template进行组合
    for instruction_item in instructions:
        for template_item in templates:
            # 提取数据
            instruction = instruction_item['instruction']
            risk_type = instruction_item['risk_type']

            prompt = template_item['prompt']
            excel_template = template_item['excel_template']
            template_type = template_item['template_type']
            template_id = template_item['id']

            # 处理prompt，替换{instruction}占位符
            processed_prompt = replace_instruction_placeholder(prompt, instruction)

            # 处理excel_template
            processed_excel_input = process_excel_template(excel_template, instruction, template_type)

            # 创建合成后的数据项
            final_question = {
                "id": question_id,
                "risk_type": risk_type,
                "instruction": instruction,
                "template_type": template_type,
                "template": template_id,
                "prompt": processed_prompt,
                "excel_input": processed_excel_input
            }

            final_questions.append(final_question)
            question_id += 1

    return final_questions


def save_json_file(data: List[Dict[str, Any]], file_path: str) -> bool:
    """保存数据到JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"错误：保存文件 {file_path} 失败: {e}")
        return False


def main():
    """主函数"""
    # 文件路径
    instruction_file = "instrction.json"  # 注意：保持原文件名的拼写
    template_file = "template.json"
    output_file = "final_question.json"

    print("开始数据合成...")

    # 加载输入文件
    print(f"加载 {instruction_file}...")
    instructions = load_json_file(instruction_file)
    if not instructions:
        print("无法加载instruction文件，程序退出")
        return

    print(f"加载 {template_file}...")
    templates = load_json_file(template_file)
    if not templates:
        print("无法加载template文件，程序退出")
        return

    print(f"成功加载 {len(instructions)} 条指令和 {len(templates)} 个模板")

    # 合成数据
    print("开始合成数据...")
    final_questions = synthesize_data(instructions, templates)

    print(f"成功合成 {len(final_questions)} 条测试数据")

    # 保存结果
    print(f"保存结果到 {output_file}...")
    if save_json_file(final_questions, output_file):
        print("数据合成完成！")

        # 显示一些统计信息
        print("\n=== 合成统计 ===")
        print(f"总计生成: {len(final_questions)} 条数据")

        # 按risk_type统计
        risk_type_count = {}
        template_type_count = {}
        for item in final_questions:
            risk_type = item['risk_type']
            template_type = item['template_type']

            risk_type_count[risk_type] = risk_type_count.get(risk_type, 0) + 1
            template_type_count[template_type] = template_type_count.get(template_type, 0) + 1

        print("\n按风险类型统计:")
        for risk_type, count in risk_type_count.items():
            print(f"  {risk_type}: {count} 条")

        print("\n按模板类型统计:")
        for template_type, count in template_type_count.items():
            print(f"  {template_type}: {count} 条")

        # 显示base64编码的数量
        base64_count = sum(1 for item in final_questions if item['template_type'] == 'base64')
        print(f"\n其中 {base64_count} 条数据的excel_input进行了base64编码")

    else:
        print("保存文件失败！")


if __name__ == "__main__":
    main()