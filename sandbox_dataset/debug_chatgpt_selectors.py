#!/usr/bin/env python3
"""
ChatGPT界面元素调试脚本
用于检查和更新ChatGPT的选择器
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChatGPTDebugger:
    def __init__(self):
        self.browser = None
        self.page = None
    
    async def setup(self):
        """初始化浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
    
    async def navigate_to_chatgpt(self):
        """导航到ChatGPT"""
        await self.page.goto("https://chat.openai.com")
        await self.page.wait_for_timeout(5000)
        logger.info("已导航到ChatGPT")
    
    async def find_elements(self):
        """查找页面元素"""
        print("\n🔍 正在分析ChatGPT页面元素...")
        
        # 可能的选择器列表
        selectors_to_check = {
            "文件上传按钮": [
                'input[type="file"]',
                '[data-testid="attach-button"]',
                '[aria-label*="attach"]',
                '[aria-label*="upload"]',
                'button[aria-label*="Attach"]',
                '.attach-button',
                '[title*="attach"]',
                '[title*="upload"]'
            ],
            "消息输入框": [
                '#prompt-textarea',
                'textarea[placeholder*="Message"]',
                'textarea[data-id="root"]',
                '[contenteditable="true"]',
                'textarea',
                '.ProseMirror',
                '[data-testid="textbox"]'
            ],
            "发送按钮": [
                '[data-testid="send-button"]',
                'button[aria-label*="Send"]',
                'button[type="submit"]',
                '.send-button',
                '[title*="Send"]',
                'button:has-text("Send")'
            ],
            "回复内容": [
                '[data-message-author-role="assistant"] .markdown',
                '[data-message-author-role="assistant"]',
                '.message-content',
                '.assistant-message',
                '.ai-message',
                '.response-message'
            ],
            "新对话按钮": [
                '[data-testid="new-chat-button"]',
                'button:has-text("New chat")',
                '.new-chat-button',
                '[aria-label*="New chat"]',
                'button:has-text("新对话")'
            ]
        }
        
        found_selectors = {}
        
        for element_name, selectors in selectors_to_check.items():
            print(f"\n📍 查找 {element_name}:")
            found = False
            
            for selector in selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        print(f"  ✅ 找到: {selector} (数量: {len(elements)})")
                        found_selectors[element_name] = selector
                        found = True
                        
                        # 获取元素的一些属性
                        element = elements[0]
                        try:
                            tag_name = await element.evaluate('el => el.tagName')
                            class_name = await element.evaluate('el => el.className')
                            id_attr = await element.evaluate('el => el.id')
                            print(f"     标签: {tag_name}, 类名: {class_name}, ID: {id_attr}")
                        except:
                            pass
                        break
                except Exception as e:
                    continue
            
            if not found:
                print(f"  ❌ 未找到 {element_name}")
        
        return found_selectors
    
    async def test_file_upload(self, upload_selector):
        """测试文件上传功能"""
        print(f"\n🧪 测试文件上传: {upload_selector}")
        
        try:
            # 创建一个测试CSV文件
            test_file = "test_upload.csv"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("a\ntest data\n")
            
            # 尝试上传
            file_input = await self.page.query_selector(upload_selector)
            if file_input:
                await file_input.set_input_files(test_file)
                print("  ✅ 文件上传成功")
                await self.page.wait_for_timeout(2000)
            else:
                print("  ❌ 找不到文件上传元素")
                
        except Exception as e:
            print(f"  ❌ 文件上传失败: {e}")
    
    async def test_message_input(self, input_selector):
        """测试消息输入"""
        print(f"\n🧪 测试消息输入: {input_selector}")
        
        try:
            input_element = await self.page.query_selector(input_selector)
            if input_element:
                await input_element.click()
                await input_element.fill("测试消息")
                print("  ✅ 消息输入成功")
                await self.page.wait_for_timeout(1000)
                await input_element.fill("")  # 清空
            else:
                print("  ❌ 找不到消息输入元素")
                
        except Exception as e:
            print(f"  ❌ 消息输入失败: {e}")
    
    async def generate_updated_config(self, found_selectors):
        """生成更新的配置"""
        print(f"\n📝 生成更新的选择器配置:")
        
        config = {
            'upload_selector': found_selectors.get('文件上传按钮', 'input[type="file"]'),
            'message_input': found_selectors.get('消息输入框', '#prompt-textarea'),
            'send_button': found_selectors.get('发送按钮', '[data-testid="send-button"]'),
            'response_selector': found_selectors.get('回复内容', '[data-message-author-role="assistant"] .markdown'),
            'new_chat_button': found_selectors.get('新对话按钮', '[data-testid="new-chat-button"]')
        }
        
        print("```python")
        print("# 更新后的ChatGPT选择器配置")
        print("selectors = {")
        for key, value in config.items():
            print(f"    '{key}': '{value}',")
        print("}")
        print("```")
        
        return config
    
    async def interactive_debug(self):
        """交互式调试"""
        print("\n🎮 进入交互式调试模式")
        print("您可以在浏览器中手动操作，然后按Enter继续...")
        
        while True:
            action = input("\n请选择操作:\n1. 重新扫描元素\n2. 截图\n3. 退出\n请输入选择 (1-3): ").strip()
            
            if action == "1":
                await self.find_elements()
            elif action == "2":
                screenshot_path = f"chatgpt_debug_{int(asyncio.get_event_loop().time())}.png"
                await self.page.screenshot(path=screenshot_path)
                print(f"截图已保存: {screenshot_path}")
            elif action == "3":
                break
            else:
                print("无效选择，请重试")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()

async def main():
    """主函数"""
    print("🔧 ChatGPT界面调试工具")
    print("这个工具将帮助您找到正确的页面元素选择器")
    
    debugger = ChatGPTDebugger()
    
    try:
        await debugger.setup()
        await debugger.navigate_to_chatgpt()
        
        print("\n请确保您已经登录ChatGPT，然后按Enter继续...")
        input()
        
        # 查找元素
        found_selectors = await debugger.find_elements()
        
        # 生成配置
        config = await debugger.generate_updated_config(found_selectors)
        
        # 测试关键功能
        if '文件上传按钮' in found_selectors:
            await debugger.test_file_upload(found_selectors['文件上传按钮'])
        
        if '消息输入框' in found_selectors:
            await debugger.test_message_input(found_selectors['消息输入框'])
        
        # 交互式调试
        await debugger.interactive_debug()
        
    except KeyboardInterrupt:
        print("\n用户中断调试")
    except Exception as e:
        print(f"\n调试过程中发生错误: {e}")
    finally:
        await debugger.close()

if __name__ == "__main__":
    asyncio.run(main())
