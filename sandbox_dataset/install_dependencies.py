#!/usr/bin/env python3
"""
安装ChatGPT自动化测试所需的依赖
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False
    
    return True

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def main():
    """主安装函数"""
    print("🚀 开始安装ChatGPT自动化测试依赖")
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装Python包
    packages = [
        "playwright",
        "asyncio",
        "pathlib"
    ]
    
    for package in packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            print(f"⚠️  {package} 安装失败，但可能已经安装")
    
    # 安装Playwright浏览器
    if not run_command("playwright install chromium", "安装Playwright浏览器"):
        print("❌ 浏览器安装失败")
        return
    
    # 验证安装
    print("\n🔍 验证安装...")
    
    try:
        # 测试导入
        import playwright
        from playwright.async_api import async_playwright
        print("✅ Playwright导入成功")
        
        # 测试脚本导入
        sys.path.append(os.getcwd())
        from automated_testing_playwright import PlaywrightLLMTester
        print("✅ 测试脚本导入成功")
        
        print("\n🎉 所有依赖安装完成！")
        print("\n📋 下一步:")
        print("1. 在浏览器中登录ChatGPT (https://chat.openai.com)")
        print("2. 运行测试: python test_chatgpt.py")
        print("3. 查看详细指南: chatgpt_testing_guide.md")
        
    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        print("请检查安装是否成功")

if __name__ == "__main__":
    main()
