#!/usr/bin/env python3
"""
快速选择测试脚本
简化版的选择性测试工具
"""

import asyncio
import json
import time
from pathlib import Path
from test_chatgpt_existing_browser import ExistingBrowserChatGPTTester

class QuickTestSelector:
    """快速测试选择器"""
    
    def __init__(self):
        self.all_tests = []
        self.load_all_tests()
    
    def load_all_tests(self):
        """加载所有测试用例"""
        data_folder = Path("data_folder")
        self.all_tests = []
        
        for risk_folder in data_folder.iterdir():
            if risk_folder.is_dir():
                risk_type = risk_folder.name
                for test_folder in risk_folder.iterdir():
                    if test_folder.is_dir():
                        prompt_file = test_folder / "prompt.txt"
                        if prompt_file.exists():
                            try:
                                with open(prompt_file, 'r', encoding='utf-8') as f:
                                    prompt = f.read().strip()
                                
                                self.all_tests.append({
                                    'id': len(self.all_tests) + 1,
                                    'path': test_folder,
                                    'risk_type': risk_type,
                                    'case_id': test_folder.name,
                                    'prompt': prompt[:100] + "..." if len(prompt) > 100 else prompt
                                })
                            except:
                                pass
        
        print(f"📊 加载了 {len(self.all_tests)} 个测试用例")
    
    def show_by_risk_type(self):
        """按风险类型显示"""
        risk_groups = {}
        for test in self.all_tests:
            risk_type = test['risk_type']
            if risk_type not in risk_groups:
                risk_groups[risk_type] = []
            risk_groups[risk_type].append(test)
        
        print("\n📋 按风险类型分组:")
        for risk_type, tests in risk_groups.items():
            print(f"\n🔸 {risk_type} ({len(tests)} 个):")
            for test in tests[:3]:  # 显示前3个
                print(f"  [{test['id']:3d}] {test['case_id']}: {test['prompt'][:50]}...")
            if len(tests) > 3:
                print(f"  ... 还有 {len(tests)-3} 个")
    
    def quick_select_by_type(self):
        """按类型快速选择"""
        risk_types = list(set(test['risk_type'] for test in self.all_tests))
        
        print(f"\n🎯 快速选择模式 - 按风险类型选择")
        print("可用的风险类型:")
        for i, risk_type in enumerate(risk_types, 1):
            count = len([t for t in self.all_tests if t['risk_type'] == risk_type])
            print(f"  {i}. {risk_type} ({count} 个)")
        
        selected_tests = []
        
        while len(selected_tests) < 10:
            remaining = 10 - len(selected_tests)
            print(f"\n还可以选择 {remaining} 个测试")
            
            try:
                choice = input("选择风险类型 (输入数字) 或 'done' 完成: ").strip()
                
                if choice.lower() == 'done':
                    break
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(risk_types):
                    selected_risk = risk_types[choice_idx]
                    available_tests = [t for t in self.all_tests 
                                     if t['risk_type'] == selected_risk and t not in selected_tests]
                    
                    if not available_tests:
                        print(f"⚠️ {selected_risk} 类型的测试都已选择")
                        continue
                    
                    count = min(remaining, len(available_tests))
                    count_input = input(f"选择几个? (最多 {count} 个，回车选择 {min(2, count)} 个): ").strip()
                    
                    if count_input:
                        count = min(int(count_input), count)
                    else:
                        count = min(2, count)
                    
                    for i in range(count):
                        selected_tests.append(available_tests[i])
                        test = available_tests[i]
                        print(f"✅ 已选择: [{test['id']}] {test['risk_type']}/{test['case_id']}")
                
                else:
                    print("❌ 无效选择")
                    
            except ValueError:
                print("❌ 请输入有效数字")
            except KeyboardInterrupt:
                print("\n用户取消")
                return []
        
        return selected_tests
    
    def quick_select_by_id(self):
        """按ID快速选择"""
        print(f"\n🎯 快速选择模式 - 按ID选择")
        print("输入测试用例ID，用逗号分隔 (例如: 1,5,10,15)")
        
        # 显示一些示例
        print("\n📋 示例测试用例:")
        for i in range(0, min(20, len(self.all_tests)), 4):
            test = self.all_tests[i]
            print(f"  [{test['id']:3d}] {test['risk_type']:>15}/{test['case_id']:>8}: {test['prompt'][:40]}...")
        
        try:
            ids_input = input(f"\n请输入ID (1-{len(self.all_tests)}): ").strip()
            if not ids_input:
                return []
            
            ids = [int(id_str.strip()) for id_str in ids_input.split(',')]
            ids = ids[:10]  # 最多10个
            
            selected_tests = []
            for test_id in ids:
                test = next((t for t in self.all_tests if t['id'] == test_id), None)
                if test:
                    selected_tests.append(test)
                    print(f"✅ 已选择: [{test['id']}] {test['risk_type']}/{test['case_id']}")
                else:
                    print(f"❌ 找不到ID {test_id}")
            
            return selected_tests
            
        except ValueError:
            print("❌ 请输入有效的数字ID")
            return []
    
    def random_select(self):
        """随机选择"""
        import random
        
        print(f"\n🎯 随机选择模式")
        
        # 按风险类型平均分配
        risk_types = list(set(test['risk_type'] for test in self.all_tests))
        per_type = 10 // len(risk_types)
        remainder = 10 % len(risk_types)
        
        selected_tests = []
        
        for i, risk_type in enumerate(risk_types):
            type_tests = [t for t in self.all_tests if t['risk_type'] == risk_type]
            count = per_type + (1 if i < remainder else 0)
            count = min(count, len(type_tests))
            
            selected = random.sample(type_tests, count)
            selected_tests.extend(selected)
            
            print(f"🎲 从 {risk_type} 随机选择了 {count} 个")
        
        return selected_tests

async def main():
    """主函数"""
    print("⚡ ChatGPT快速选择测试工具")
    
    selector = QuickTestSelector()
    
    if not selector.all_tests:
        print("❌ 没有找到测试用例")
        return
    
    # 显示概览
    selector.show_by_risk_type()
    
    # 选择模式
    print(f"\n🎯 选择测试模式:")
    print("1. 按风险类型选择")
    print("2. 按ID选择")
    print("3. 随机选择")
    
    mode = input("请选择模式 (1-3): ").strip()
    
    if mode == "1":
        selected_tests = selector.quick_select_by_type()
    elif mode == "2":
        selected_tests = selector.quick_select_by_id()
    elif mode == "3":
        selected_tests = selector.random_select()
    else:
        print("❌ 无效选择")
        return
    
    if not selected_tests:
        print("❌ 没有选择任何测试")
        return
    
    # 显示选择结果
    print(f"\n📋 已选择 {len(selected_tests)} 个测试:")
    for i, test in enumerate(selected_tests, 1):
        print(f"  {i}. [{test['id']}] {test['risk_type']}/{test['case_id']}")
    
    # 确认执行
    confirm = input(f"\n是否执行这 {len(selected_tests)} 个测试? (y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    # 执行测试
    tester = ExistingBrowserChatGPTTester()
    
    try:
        # 连接浏览器
        print("\n🔗 连接Chrome浏览器...")
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            print("请先运行: python start_chrome_debug.py")
            return
        
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        await tester.find_and_update_selectors()
        print("✅ 已连接到ChatGPT页面")
        
        # 执行测试
        results = []
        for i, test in enumerate(selected_tests, 1):
            print(f"\n🧪 [{i}/{len(selected_tests)}] 测试: {test['risk_type']}/{test['case_id']}")
            
            result = await tester.run_single_test(test['path'])
            results.append(result)
            
            success_count = sum(1 for r in results if r.get('test_success'))
            print(f"进度: {i}/{len(selected_tests)}, 成功: {success_count}")
            
            if i < len(selected_tests):
                await asyncio.sleep(3)
        
        # 结果统计
        successful = sum(1 for r in results if r.get('test_success'))
        saved_files = sum(1 for r in results if r.get('saved_file'))
        
        print(f"\n🎉 测试完成!")
        print(f"总测试: {len(results)}")
        print(f"成功: {successful} ({successful/len(results)*100:.1f}%)")
        print(f"保存文件: {saved_files}")
        
        # 保存结果
        result_file = f"quick_test_results_{int(time.time())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存: {result_file}")
        
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断")
    except Exception as e:
        print(f"\n❌ 错误: {e}")
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
