#!/usr/bin/env python3
"""
完整的测试数据生成流水线
执行以下步骤：
1. 清理data_folder中的所有生成文件
2. 执行data_synthesis.py生成新的final_question.json
3. 执行generate_test_files.py生成测试文件夹结构
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def print_step(step_num, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print(f"{'='*60}")

def clean_data_folder(base_folder="data_folder"):
    """
    步骤1: 清理data_folder中的所有生成文件
    """
    print_step(1, "清理data_folder中的所有生成文件")
    
    base_path = Path(base_folder)
    
    if not base_path.exists():
        print(f"文件夹 {base_folder} 不存在，跳过清理步骤")
        return True
    
    try:
        # 统计清理前的文件数量
        total_files = 0
        total_folders = 0
        
        for root, dirs, files in os.walk(base_path):
            total_files += len(files)
            total_folders += len(dirs)
        
        print(f"清理前统计:")
        print(f"  文件夹数量: {total_folders}")
        print(f"  文件数量: {total_files}")
        
        # 删除整个data_folder文件夹
        print(f"正在删除 {base_folder} 文件夹...")
        shutil.rmtree(base_path)
        
        print(f"✅ 成功清理 {base_folder} 文件夹")
        print(f"   删除了 {total_folders} 个文件夹和 {total_files} 个文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def run_data_synthesis():
    """
    步骤2: 执行data_synthesis.py生成新的final_question.json
    """
    print_step(2, "执行data_synthesis.py生成新的final_question.json")
    
    # 检查data_synthesis.py是否存在
    if not Path("data_synthesis.py").exists():
        print("❌ 错误: data_synthesis.py 文件不存在")
        return False
    
    # 检查输入文件是否存在
    if not Path("instrction.json").exists():
        print("❌ 错误: instrction.json 文件不存在")
        return False
    
    if not Path("template.json").exists():
        print("❌ 错误: template.json 文件不存在")
        return False
    
    try:
        print("正在执行 data_synthesis.py...")
        
        # 执行data_synthesis.py
        result = subprocess.run(
            [sys.executable, "data_synthesis.py"],
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        if result.returncode == 0:
            print("✅ data_synthesis.py 执行成功")
            
            # 显示输出
            if result.stdout:
                print("输出信息:")
                for line in result.stdout.strip().split('\n'):
                    print(f"  {line}")
            
            # 验证final_question.json是否生成
            if Path("final_question.json").exists():
                print("✅ final_question.json 文件已生成")
                return True
            else:
                print("❌ 错误: final_question.json 文件未生成")
                return False
        else:
            print(f"❌ data_synthesis.py 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print("错误信息:")
                for line in result.stderr.strip().split('\n'):
                    print(f"  {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 错误: data_synthesis.py 执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行data_synthesis.py时发生错误: {e}")
        return False

def run_generate_test_files():
    """
    步骤3: 执行generate_test_files.py生成测试文件夹结构
    """
    print_step(3, "执行generate_test_files.py生成测试文件夹结构")
    
    # 检查generate_test_files.py是否存在
    if not Path("generate_test_files.py").exists():
        print("❌ 错误: generate_test_files.py 文件不存在")
        return False
    
    # 检查final_question.json是否存在
    if not Path("final_question.json").exists():
        print("❌ 错误: final_question.json 文件不存在")
        return False
    
    try:
        print("正在执行 generate_test_files.py...")
        
        # 执行generate_test_files.py
        result = subprocess.run(
            [sys.executable, "generate_test_files.py"],
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print("✅ generate_test_files.py 执行成功")
            
            # 显示输出
            if result.stdout:
                print("输出信息:")
                for line in result.stdout.strip().split('\n'):
                    print(f"  {line}")
            
            # 验证data_folder是否生成
            if Path("data_folder").exists():
                print("✅ data_folder 文件夹已生成")
                return True
            else:
                print("❌ 错误: data_folder 文件夹未生成")
                return False
        else:
            print(f"❌ generate_test_files.py 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print("错误信息:")
                for line in result.stderr.strip().split('\n'):
                    print(f"  {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 错误: generate_test_files.py 执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行generate_test_files.py时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始执行完整的测试数据生成流水线")
    print(f"当前工作目录: {os.getcwd()}")
    
    start_time = time.time()
    
    # 步骤1: 清理data_folder
    if not clean_data_folder():
        print("\n❌ 流水线执行失败: 清理步骤失败")
        sys.exit(1)
    
    # 步骤2: 执行data_synthesis.py
    if not run_data_synthesis():
        print("\n❌ 流水线执行失败: 数据合成步骤失败")
        sys.exit(1)
    
    # 步骤3: 执行generate_test_files.py
    if not run_generate_test_files():
        print("\n❌ 流水线执行失败: 文件生成步骤失败")
        sys.exit(1)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print(f"\n{'='*60}")
    print("🎉 流水线执行完成!")
    print(f"{'='*60}")
    print(f"总耗时: {elapsed_time:.2f} 秒")
    print("\n✅ 所有步骤都已成功完成:")
    print("  1. ✅ 清理了旧的data_folder文件夹")
    print("  2. ✅ 生成了新的final_question.json文件")
    print("  3. ✅ 创建了新的data_folder测试文件结构")
    print("\n🎯 您现在可以使用data_folder中的测试文件来验证安全沙箱的有效性!")

if __name__ == "__main__":
    main()
