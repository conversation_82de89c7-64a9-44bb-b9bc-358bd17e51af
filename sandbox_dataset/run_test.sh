#!/bin/bash
echo "🚀 运行ChatGPT测试"
source chatgpt_test_env/bin/activate

echo "请选择测试脚本:"
echo "1. 现有浏览器测试 (推荐)"
echo "2. 更新版测试"
echo "3. 选择性测试 (自选10个样本)"
echo "4. 快速选择测试 (简化版)"
echo "5. 批量测试"
read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        chatgpt_test_env/bin/python test_chatgpt_existing_browser.py
        ;;
    2)
        chatgpt_test_env/bin/python test_chatgpt_updated.py
        ;;
    3)
        chatgpt_test_env/bin/python selective_test_chatgpt.py
        ;;
    4)
        chatgpt_test_env/bin/python quick_select_test.py
        ;;
    5)
        chatgpt_test_env/bin/python batch_test_chatgpt.py
        ;;
    *)
        echo "无效选择"
        ;;
esac

read -p "按Enter键退出..."
