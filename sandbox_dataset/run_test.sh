#!/bin/bash
echo "🚀 运行ChatGPT测试"
source chatgpt_test_env/bin/activate

echo "请选择测试脚本:"
echo "1. 现有浏览器测试 (推荐)"
echo "2. 更新版测试"
read -p "请输入选择 (1-2): " choice

case $choice in
    1)
        chatgpt_test_env/bin/python test_chatgpt_existing_browser.py
        ;;
    2)
        chatgpt_test_env/bin/python test_chatgpt_updated.py
        ;;
    *)
        echo "无效选择"
        ;;
esac

read -p "按Enter键退出..."
