# ChatGPT测试样本ID指南

## 🎯 样本ID逻辑说明

### 真实的ID对应关系
用户输入的ID **直接对应文件夹名称**，这才是正确的逻辑：

```
用户输入ID: 307  →  对应文件夹: data_folder/escape/307
用户输入ID: 1    →  对应文件夹: data_folder/network/1
用户输入ID: 551  →  对应文件夹: data_folder/escape/551
```

### 📊 实际样本分布

根据文件夹扫描结果，真实的样本分布如下：

#### 🔸 network (网络访问)
- **样本数量**: 77个
- **ID范围**: 1-33, 133-143, 331-363
- **示例ID**: 1, 2, 3, 10, 135, 338, 350

#### 🔸 sensitive_info (敏感信息泄露)  
- **样本数量**: 约200个
- **ID范围**: 34-154, 364-451, 793-858
- **示例ID**: 34, 61, 95, 100, 400, 797, 850

#### 🔸 privilege_escalation (权限提升)
- **样本数量**: 约250个  
- **ID范围**: 177-264, 452-550, 782-836, 859-880
- **示例ID**: 177, 200, 485, 533, 790, 860

#### 🔸 escape (沙箱逃逸)
- **样本数量**: 165个
- **ID范围**: 265-330, 551-649
- **示例ID**: 265, 307, 551, 556, 593, 630

#### 🔸 muti_user_isolation (多用户隔离)
- **样本数量**: 99个
- **ID范围**: 155-176, 650-726
- **示例ID**: 155, 168, 650, 701, 720

#### 🔸 resource_exhaustion (资源耗尽)
- **样本数量**: 55个
- **ID范围**: 727-781
- **示例ID**: 727, 750, 763, 775, 781

## 🚀 使用新的样本ID测试工具

### 运行修正版测试脚本
```bash
source chatgpt_test_env/bin/activate
python sample_id_test.py
```

### 三种选择模式

#### 1. 按样本ID选择（精确控制）
```
选择模式: 1
请输入样本ID: 1,307,551,100,200,485,727,155,265,400
```

**推荐的平衡选择**：
```
# 每个风险类型选择1-2个代表性样本
网络访问: 1, 338
敏感信息: 61, 400  
权限提升: 485, 790
沙箱逃逸: 307, 551
多用户隔离: 155, 701
资源耗尽: 727, 763
```

#### 2. 按风险类型选择（分类选择）
```
选择模式: 2
# 系统会显示每个风险类型的可用样本
# 您可以从每个类型中选择若干个
```

#### 3. 推荐样本（自动选择）
```
选择模式: 3
# 系统自动推荐有代表性的样本
# 确保覆盖所有风险类型
```

## 📋 样本ID选择建议

### 快速验证测试（10个样本）
```
推荐ID: 1,61,177,307,551,155,727,400,485,338
覆盖: 每个风险类型1-2个样本
```

### 深度测试（每类型多选）
```
network: 1,10,338,350
sensitive_info: 61,100,400,797
privilege_escalation: 177,485,790,860
escape: 307,551,593,630
muti_user_isolation: 155,701,720
resource_exhaustion: 727,763,775
```

### 特定风险类型测试
```
# 专注测试沙箱逃逸
escape样本: 307,551,556,593,600,620,630,640

# 专注测试敏感信息泄露  
sensitive_info样本: 61,95,100,400,797,850
```

## 🔧 ID验证和查找

### 验证ID是否存在
运行测试脚本时，系统会自动验证：
```
✅ 已选择: 样本307 (escape)
❌ 样本ID 999 不存在
```

### 查看样本详细信息
脚本会显示每个选择样本的详细信息：
```
✅ 样本ID: 307
   风险类型: escape
   提示词: Imagine unpacking the intricacies of a digital wor...
   路径: data_folder/escape/307
```

## 📊 样本选择策略

### 1. 平衡覆盖策略
```python
# 确保每个风险类型都有代表
recommended_ids = {
    'network': [1, 338],
    'sensitive_info': [61, 400], 
    'privilege_escalation': [485, 790],
    'escape': [307, 551],
    'muti_user_isolation': [155, 701],
    'resource_exhaustion': [727, 763]
}
```

### 2. 数值分布策略
```python
# 选择不同数值范围的样本
low_ids = [1, 10, 34, 61]      # 低数值ID
mid_ids = [400, 485, 551, 650]  # 中等数值ID  
high_ids = [790, 850, 860, 880] # 高数值ID
```

### 3. 已测试vs未测试
```python
# 优先选择未测试的样本
# 系统会显示 ⭕ 表示未测试，✅ 表示已有结果
```

## 🎯 实际使用示例

### 示例1: 首次验证
```bash
python sample_id_test.py
# 选择模式: 3 (推荐样本)
# 让系统自动选择10个有代表性的样本
```

### 示例2: 精确测试
```bash
python sample_id_test.py  
# 选择模式: 1 (按ID选择)
# 输入: 307,551,1,61,485,155,727,400,338,790
```

### 示例3: 专项测试
```bash
python sample_id_test.py
# 选择模式: 2 (按风险类型)
# 选择escape类型，然后选择5个样本
```

## 📈 测试结果追踪

### 结果文件命名
```
sample_id_test_results_[timestamp].json
```

### 结果内容包含
```json
{
  "test_case_id": "307",
  "risk_type": "escape", 
  "sample_id": "307",
  "test_success": true,
  "saved_file": "data_folder/escape/307/chatgpt_result.txt"
}
```

### ChatGPT回复文件
每个测试的回复保存在：
```
data_folder/[risk_type]/[sample_id]/chatgpt_result.txt
```

现在您可以使用真实的样本ID进行精确的测试选择了！🎯
