#!/usr/bin/env python3
"""
基于真实样本ID的ChatGPT测试脚本
用户输入的ID直接对应文件夹名称
"""

import asyncio
import json
import time
from pathlib import Path
from test_chatgpt_existing_browser import ExistingBrowserChatGPTTester

class SampleIDTester:
    """基于样本ID的测试器"""
    
    def __init__(self):
        self.sample_map = {}  # 样本ID到路径的映射
        self.load_sample_map()
    
    def load_sample_map(self):
        """加载样本ID映射"""
        data_folder = Path("data_folder")
        self.sample_map = {}
        
        print("🔍 扫描样本ID...")
        
        for risk_folder in data_folder.iterdir():
            if risk_folder.is_dir():
                risk_type = risk_folder.name
                for sample_folder in risk_folder.iterdir():
                    if sample_folder.is_dir():
                        sample_id = sample_folder.name
                        prompt_file = sample_folder / "prompt.txt"
                        
                        if prompt_file.exists():
                            try:
                                with open(prompt_file, 'r', encoding='utf-8') as f:
                                    prompt = f.read().strip()
                                
                                self.sample_map[sample_id] = {
                                    'path': sample_folder,
                                    'risk_type': risk_type,
                                    'sample_id': sample_id,
                                    'prompt_preview': prompt[:100] + "..." if len(prompt) > 100 else prompt,
                                    'has_result': (sample_folder / "chatgpt_result.txt").exists()
                                }
                            except:
                                pass
        
        print(f"✅ 找到 {len(self.sample_map)} 个样本")
    
    def show_sample_overview(self):
        """显示样本概览"""
        risk_groups = {}
        for sample_id, info in self.sample_map.items():
            risk_type = info['risk_type']
            if risk_type not in risk_groups:
                risk_groups[risk_type] = []
            risk_groups[risk_type].append(sample_id)
        
        print(f"\n📊 样本分布概览:")
        for risk_type, sample_ids in risk_groups.items():
            sample_ids.sort(key=int)  # 按数字排序
            print(f"\n🔸 {risk_type} ({len(sample_ids)} 个样本):")
            print(f"   ID范围: {sample_ids[0]} - {sample_ids[-1]}")
            print(f"   示例ID: {', '.join(sample_ids[:5])}")
            if len(sample_ids) > 5:
                print(f"   ... 还有 {len(sample_ids)-5} 个")
    
    def show_sample_details(self, sample_ids):
        """显示指定样本的详细信息"""
        print(f"\n📋 样本详细信息:")
        print("=" * 80)
        
        for sample_id in sample_ids:
            if sample_id in self.sample_map:
                info = self.sample_map[sample_id]
                status = "✅" if info['has_result'] else "⭕"
                print(f"{status} 样本ID: {sample_id}")
                print(f"   风险类型: {info['risk_type']}")
                print(f"   提示词: {info['prompt_preview']}")
                print(f"   路径: {info['path']}")
                print()
            else:
                print(f"❌ 样本ID {sample_id} 不存在")
                print()
    
    def select_samples_by_id(self, max_samples=10):
        """按ID选择样本"""
        selected_samples = []
        
        while len(selected_samples) < max_samples:
            remaining = max_samples - len(selected_samples)
            print(f"\n🎯 请输入样本ID (还可选择 {remaining} 个)")
            print("提示: 输入多个ID用逗号分隔，如: 1,307,551,100,200")
            
            ids_input = input("请输入样本ID: ").strip()
            if not ids_input:
                break
            
            try:
                # 解析输入的ID
                input_ids = [id_str.strip() for id_str in ids_input.split(',')]
                
                for sample_id in input_ids:
                    if len(selected_samples) >= max_samples:
                        print(f"⚠️ 已达到最大选择数量 {max_samples}")
                        break
                    
                    if sample_id in self.sample_map:
                        if sample_id not in [s['sample_id'] for s in selected_samples]:
                            info = self.sample_map[sample_id]
                            selected_samples.append(info)
                            print(f"✅ 已选择: 样本{sample_id} ({info['risk_type']})")
                        else:
                            print(f"⚠️ 样本{sample_id} 已经选择过了")
                    else:
                        print(f"❌ 样本ID {sample_id} 不存在")
                        
            except Exception as e:
                print(f"❌ 输入格式错误: {e}")
            
            if len(selected_samples) >= max_samples:
                break
            
            # 询问是否继续
            if len(selected_samples) > 0:
                continue_choice = input(f"\n已选择 {len(selected_samples)} 个样本，是否继续选择? (y/N): ").strip().lower()
                if continue_choice != 'y':
                    break
        
        return selected_samples
    
    def select_samples_by_risk_type(self, max_samples=10):
        """按风险类型选择样本"""
        risk_groups = {}
        for sample_id, info in self.sample_map.items():
            risk_type = info['risk_type']
            if risk_type not in risk_groups:
                risk_groups[risk_type] = []
            risk_groups[risk_type].append(info)
        
        # 按样本ID排序
        for risk_type in risk_groups:
            risk_groups[risk_type].sort(key=lambda x: int(x['sample_id']))
        
        print(f"\n🎯 按风险类型选择样本")
        risk_types = list(risk_groups.keys())
        
        for i, risk_type in enumerate(risk_types, 1):
            count = len(risk_groups[risk_type])
            sample_range = f"{risk_groups[risk_type][0]['sample_id']}-{risk_groups[risk_type][-1]['sample_id']}"
            print(f"  {i}. {risk_type} ({count}个, ID范围: {sample_range})")
        
        selected_samples = []
        
        while len(selected_samples) < max_samples:
            remaining = max_samples - len(selected_samples)
            print(f"\n还可选择 {remaining} 个样本")
            
            try:
                choice = input("选择风险类型 (输入数字) 或 'done' 完成: ").strip()
                
                if choice.lower() == 'done':
                    break
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(risk_types):
                    selected_risk = risk_types[choice_idx]
                    available_samples = [s for s in risk_groups[selected_risk] 
                                       if s not in selected_samples]
                    
                    if not available_samples:
                        print(f"⚠️ {selected_risk} 类型的样本都已选择")
                        continue
                    
                    # 显示可用样本
                    print(f"\n{selected_risk} 类型的可用样本:")
                    for i, sample in enumerate(available_samples[:10], 1):
                        status = "✅" if sample['has_result'] else "⭕"
                        print(f"  {i}. {status} 样本{sample['sample_id']}: {sample['prompt_preview'][:50]}...")
                    
                    if len(available_samples) > 10:
                        print(f"  ... 还有 {len(available_samples)-10} 个样本")
                    
                    # 选择数量
                    max_count = min(remaining, len(available_samples))
                    count_input = input(f"选择几个样本? (最多 {max_count} 个): ").strip()
                    
                    if count_input:
                        count = min(int(count_input), max_count)
                    else:
                        count = min(2, max_count)
                    
                    # 添加样本
                    for i in range(count):
                        selected_samples.append(available_samples[i])
                        sample = available_samples[i]
                        print(f"✅ 已选择: 样本{sample['sample_id']} ({sample['risk_type']})")
                
                else:
                    print("❌ 无效选择")
                    
            except ValueError:
                print("❌ 请输入有效数字")
            except KeyboardInterrupt:
                print("\n用户取消")
                break
        
        return selected_samples
    
    def recommend_samples(self, max_samples=10):
        """推荐有代表性的样本"""
        print(f"\n🎯 推荐有代表性的样本")
        
        risk_groups = {}
        for sample_id, info in self.sample_map.items():
            risk_type = info['risk_type']
            if risk_type not in risk_groups:
                risk_groups[risk_type] = []
            risk_groups[risk_type].append(info)
        
        # 每个风险类型选择1-2个代表性样本
        recommended = []
        per_type = max_samples // len(risk_groups)
        remainder = max_samples % len(risk_groups)
        
        for i, (risk_type, samples) in enumerate(risk_groups.items()):
            # 按样本ID排序
            samples.sort(key=lambda x: int(x['sample_id']))
            
            # 选择数量
            count = per_type + (1 if i < remainder else 0)
            count = min(count, len(samples))
            
            # 选择代表性样本（开头、中间、末尾）
            if count == 1:
                selected = [samples[0]]
            elif count == 2:
                selected = [samples[0], samples[len(samples)//2]]
            else:
                indices = [0, len(samples)//2, len(samples)-1]
                selected = [samples[idx] for idx in indices[:count]]
            
            recommended.extend(selected)
            
            print(f"📌 {risk_type}: 推荐样本 {', '.join([s['sample_id'] for s in selected])}")
        
        return recommended[:max_samples]

async def main():
    """主函数"""
    print("🎯 基于样本ID的ChatGPT测试工具")
    print("直接使用真实的样本ID进行测试")
    
    tester_manager = SampleIDTester()
    
    if not tester_manager.sample_map:
        print("❌ 没有找到可用的样本")
        return
    
    # 显示概览
    tester_manager.show_sample_overview()
    
    # 选择模式
    print(f"\n🎯 选择测试模式:")
    print("1. 按样本ID选择")
    print("2. 按风险类型选择")
    print("3. 推荐样本")
    
    mode = input("请选择模式 (1-3): ").strip()
    
    if mode == "1":
        selected_samples = tester_manager.select_samples_by_id(max_samples=10)
    elif mode == "2":
        selected_samples = tester_manager.select_samples_by_risk_type(max_samples=10)
    elif mode == "3":
        selected_samples = tester_manager.recommend_samples(max_samples=10)
    else:
        print("❌ 无效选择")
        return
    
    if not selected_samples:
        print("❌ 没有选择任何样本")
        return
    
    # 显示选择结果
    print(f"\n📋 已选择 {len(selected_samples)} 个样本:")
    tester_manager.show_sample_details([s['sample_id'] for s in selected_samples])
    
    # 确认执行
    confirm = input(f"是否执行这 {len(selected_samples)} 个测试? (y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    # 执行测试
    tester = ExistingBrowserChatGPTTester()
    
    try:
        # 连接浏览器
        print("\n🔗 连接Chrome浏览器...")
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            print("请先运行: python start_chrome_debug.py")
            return
        
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        await tester.find_and_update_selectors()
        print("✅ 已连接到ChatGPT页面")
        
        # 执行测试
        results = []
        for i, sample in enumerate(selected_samples, 1):
            print(f"\n🧪 [{i}/{len(selected_samples)}] 测试样本: {sample['sample_id']} ({sample['risk_type']})")
            
            # 开始新对话
            print("🔄 开始新对话...")
            new_chat_success = await tester.start_new_chat()
            if new_chat_success:
                print("✅ 新对话已开始")
            else:
                print("⚠️ 新对话开始失败")
            
            result = await tester.run_single_test(sample['path'])
            results.append(result)
            
            success_count = sum(1 for r in results if r.get('test_success'))
            new_chat_count = sum(1 for r in results if r.get('new_chat_started', False))
            print(f"进度: {i}/{len(selected_samples)}, 成功: {success_count}, 新对话: {new_chat_count}")
            
            if i < len(selected_samples):
                await asyncio.sleep(3)
        
        # 结果统计
        successful = sum(1 for r in results if r.get('test_success'))
        saved_files = sum(1 for r in results if r.get('saved_file'))
        new_chats = sum(1 for r in results if r.get('new_chat_started', False))
        
        print(f"\n🎉 测试完成!")
        print(f"总测试: {len(results)}")
        print(f"成功: {successful} ({successful/len(results)*100:.1f}%)")
        print(f"保存文件: {saved_files}")
        print(f"新对话: {new_chats} ({new_chats/len(results)*100:.1f}%)")
        
        # 保存结果
        result_file = f"sample_id_test_results_{int(time.time())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存: {result_file}")
        
        # 显示测试的样本ID
        tested_ids = [s['sample_id'] for s in selected_samples]
        print(f"\n📋 本次测试的样本ID: {', '.join(tested_ids)}")
        
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断")
    except Exception as e:
        print(f"\n❌ 错误: {e}")
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
