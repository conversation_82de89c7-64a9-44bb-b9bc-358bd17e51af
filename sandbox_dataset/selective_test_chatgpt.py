#!/usr/bin/env python3
"""
ChatGPT选择性测试脚本
允许用户自由选择要测试的样本
"""

import asyncio
import json
import time
from pathlib import Path
from test_chatgpt_existing_browser import ExistingBrowserChatGPTTester
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SelectiveTestManager:
    """选择性测试管理器"""
    
    def __init__(self):
        self.all_test_cases = []
        self.selected_tests = []
        self.data_folder = Path("data_folder")
    
    def scan_all_test_cases(self):
        """扫描所有可用的测试用例"""
        self.all_test_cases = []
        
        if not self.data_folder.exists():
            print(f"❌ 测试数据文件夹不存在: {self.data_folder}")
            return
        
        for risk_type_folder in self.data_folder.iterdir():
            if risk_type_folder.is_dir():
                risk_type = risk_type_folder.name
                
                for test_case_folder in risk_type_folder.iterdir():
                    if test_case_folder.is_dir():
                        # 读取测试信息
                        prompt_file = test_case_folder / "prompt.txt"
                        excel_file = test_case_folder / "excel.csv"
                        
                        if prompt_file.exists() and excel_file.exists():
                            # 读取提示词预览
                            try:
                                with open(prompt_file, 'r', encoding='utf-8') as f:
                                    prompt_preview = f.read().strip()[:100] + "..."
                            except:
                                prompt_preview = "无法读取提示词"
                            
                            # 读取测试数据预览
                            try:
                                with open(excel_file, 'r', encoding='utf-8') as f:
                                    lines = f.readlines()
                                    if len(lines) > 1:
                                        test_data = lines[1].strip()  # 跳过表头
                                    else:
                                        test_data = "无测试数据"
                            except:
                                test_data = "无法读取测试数据"
                            
                            # 检查是否已有结果
                            result_file = test_case_folder / "chatgpt_result.txt"
                            has_result = result_file.exists()
                            
                            test_case = {
                                'id': len(self.all_test_cases) + 1,
                                'risk_type': risk_type,
                                'case_id': test_case_folder.name,
                                'folder_path': test_case_folder,
                                'prompt_preview': prompt_preview,
                                'test_data': test_data,
                                'has_result': has_result,
                                'result_file': result_file if has_result else None
                            }
                            
                            self.all_test_cases.append(test_case)
        
        print(f"✅ 扫描完成，找到 {len(self.all_test_cases)} 个测试用例")
    
    def show_test_cases_by_risk_type(self):
        """按风险类型显示测试用例"""
        risk_types = {}
        for test_case in self.all_test_cases:
            risk_type = test_case['risk_type']
            if risk_type not in risk_types:
                risk_types[risk_type] = []
            risk_types[risk_type].append(test_case)
        
        print(f"\n📋 按风险类型分组的测试用例:")
        for risk_type, cases in risk_types.items():
            print(f"\n🔸 {risk_type} ({len(cases)} 个用例):")
            for case in cases[:5]:  # 只显示前5个
                status = "✅" if case['has_result'] else "⭕"
                print(f"  {status} [{case['id']:3d}] {case['case_id']}: {case['prompt_preview'][:50]}...")
            
            if len(cases) > 5:
                print(f"    ... 还有 {len(cases)-5} 个用例")
        
        return risk_types
    
    def show_test_cases_list(self, start=0, count=20):
        """显示测试用例列表"""
        end = min(start + count, len(self.all_test_cases))
        
        print(f"\n📋 测试用例列表 ({start+1}-{end}/{len(self.all_test_cases)}):")
        print("=" * 100)
        print(f"{'ID':>3} {'状态':>4} {'风险类型':>15} {'用例ID':>8} {'提示词预览':>50}")
        print("-" * 100)
        
        for i in range(start, end):
            case = self.all_test_cases[i]
            status = "✅" if case['has_result'] else "⭕"
            print(f"{case['id']:>3} {status:>4} {case['risk_type']:>15} {case['case_id']:>8} {case['prompt_preview'][:45]:>50}")
    
    def select_tests_interactive(self, max_tests=10):
        """交互式选择测试用例"""
        self.selected_tests = []
        
        while len(self.selected_tests) < max_tests:
            remaining = max_tests - len(self.selected_tests)
            print(f"\n🎯 请选择测试用例 (还可选择 {remaining} 个):")
            print("1. 按ID选择")
            print("2. 按风险类型选择")
            print("3. 查看当前选择")
            print("4. 查看测试用例列表")
            print("5. 搜索测试用例")
            print("6. 完成选择")
            
            choice = input("请输入选择 (1-6): ").strip()
            
            if choice == "1":
                self.select_by_id(remaining)
            elif choice == "2":
                self.select_by_risk_type(remaining)
            elif choice == "3":
                self.show_selected_tests()
            elif choice == "4":
                start = int(input(f"从第几个开始显示 (1-{len(self.all_test_cases)}): ") or "1") - 1
                self.show_test_cases_list(start, 20)
            elif choice == "5":
                self.search_test_cases(remaining)
            elif choice == "6":
                break
            else:
                print("❌ 无效选择")
        
        if not self.selected_tests:
            print("⚠️ 未选择任何测试用例")
            return False
        
        print(f"\n✅ 已选择 {len(self.selected_tests)} 个测试用例")
        return True
    
    def select_by_id(self, max_remaining):
        """按ID选择测试用例"""
        try:
            ids_input = input(f"请输入测试用例ID (用逗号分隔，最多{max_remaining}个): ").strip()
            if not ids_input:
                return
            
            ids = [int(id_str.strip()) for id_str in ids_input.split(',')]
            added_count = 0
            
            for test_id in ids:
                if added_count >= max_remaining:
                    print(f"⚠️ 已达到最大选择数量 {max_remaining}")
                    break
                
                # 查找测试用例
                test_case = next((case for case in self.all_test_cases if case['id'] == test_id), None)
                if test_case:
                    if test_case not in self.selected_tests:
                        self.selected_tests.append(test_case)
                        added_count += 1
                        print(f"✅ 已添加: [{test_id}] {test_case['risk_type']}/{test_case['case_id']}")
                    else:
                        print(f"⚠️ 测试用例 {test_id} 已经选择过了")
                else:
                    print(f"❌ 找不到ID为 {test_id} 的测试用例")
                    
        except ValueError:
            print("❌ 请输入有效的数字ID")
    
    def select_by_risk_type(self, max_remaining):
        """按风险类型选择测试用例"""
        risk_types = {}
        for case in self.all_test_cases:
            risk_type = case['risk_type']
            if risk_type not in risk_types:
                risk_types[risk_type] = []
            risk_types[risk_type].append(case)
        
        print(f"\n📋 可用的风险类型:")
        risk_type_list = list(risk_types.keys())
        for i, risk_type in enumerate(risk_type_list, 1):
            count = len(risk_types[risk_type])
            print(f"  {i}. {risk_type} ({count} 个用例)")
        
        try:
            choice = int(input("请选择风险类型 (输入数字): ").strip())
            if 1 <= choice <= len(risk_type_list):
                selected_risk_type = risk_type_list[choice - 1]
                available_cases = [case for case in risk_types[selected_risk_type] 
                                 if case not in self.selected_tests]
                
                if not available_cases:
                    print(f"⚠️ {selected_risk_type} 类型的测试用例都已选择")
                    return
                
                count = min(max_remaining, len(available_cases))
                count = int(input(f"选择多少个用例? (最多 {count} 个): ").strip() or str(count))
                count = min(count, max_remaining, len(available_cases))
                
                for i in range(count):
                    case = available_cases[i]
                    self.selected_tests.append(case)
                    print(f"✅ 已添加: [{case['id']}] {case['risk_type']}/{case['case_id']}")
                    
            else:
                print("❌ 无效的选择")
                
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def search_test_cases(self, max_remaining):
        """搜索测试用例"""
        keyword = input("请输入搜索关键词 (搜索提示词内容): ").strip().lower()
        if not keyword:
            return
        
        matching_cases = []
        for case in self.all_test_cases:
            if (keyword in case['prompt_preview'].lower() or 
                keyword in case['test_data'].lower() or
                keyword in case['risk_type'].lower()):
                matching_cases.append(case)
        
        if not matching_cases:
            print(f"❌ 没有找到包含 '{keyword}' 的测试用例")
            return
        
        print(f"\n🔍 找到 {len(matching_cases)} 个匹配的测试用例:")
        for i, case in enumerate(matching_cases[:10], 1):  # 只显示前10个
            status = "✅" if case['has_result'] else "⭕"
            print(f"  {i}. {status} [{case['id']}] {case['risk_type']}/{case['case_id']}: {case['prompt_preview'][:50]}...")
        
        if len(matching_cases) > 10:
            print(f"    ... 还有 {len(matching_cases)-10} 个匹配结果")
        
        # 选择匹配的用例
        try:
            indices_input = input(f"请输入要选择的序号 (1-{min(10, len(matching_cases))}，用逗号分隔): ").strip()
            if indices_input:
                indices = [int(idx.strip()) for idx in indices_input.split(',')]
                added_count = 0
                
                for idx in indices:
                    if added_count >= max_remaining:
                        break
                    if 1 <= idx <= min(10, len(matching_cases)):
                        case = matching_cases[idx - 1]
                        if case not in self.selected_tests:
                            self.selected_tests.append(case)
                            added_count += 1
                            print(f"✅ 已添加: [{case['id']}] {case['risk_type']}/{case['case_id']}")
                        else:
                            print(f"⚠️ 测试用例已经选择过了")
                    else:
                        print(f"❌ 无效的序号: {idx}")
                        
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def show_selected_tests(self):
        """显示已选择的测试用例"""
        if not self.selected_tests:
            print("📝 还没有选择任何测试用例")
            return
        
        print(f"\n📋 已选择的测试用例 ({len(self.selected_tests)} 个):")
        print("=" * 80)
        for i, case in enumerate(self.selected_tests, 1):
            status = "✅" if case['has_result'] else "⭕"
            print(f"{i:>2}. {status} [{case['id']:>3}] {case['risk_type']:>15}/{case['case_id']:>8}")
            print(f"     提示词: {case['prompt_preview'][:60]}...")
            print(f"     数据: {case['test_data'][:60]}...")
            print()
    
    def save_test_selection(self, filename="selected_tests.json"):
        """保存测试选择"""
        selection_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_selected': len(self.selected_tests),
            'selected_tests': [
                {
                    'id': case['id'],
                    'risk_type': case['risk_type'],
                    'case_id': case['case_id'],
                    'folder_path': str(case['folder_path']),
                    'has_result': case['has_result']
                }
                for case in self.selected_tests
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(selection_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 测试选择已保存到: {filename}")
    
    def load_test_selection(self, filename="selected_tests.json"):
        """加载测试选择"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                selection_data = json.load(f)
            
            self.selected_tests = []
            for saved_test in selection_data['selected_tests']:
                # 根据保存的信息找到对应的测试用例
                test_case = next((case for case in self.all_test_cases 
                                if case['id'] == saved_test['id']), None)
                if test_case:
                    self.selected_tests.append(test_case)
            
            print(f"📂 已加载 {len(self.selected_tests)} 个测试选择")
            return True
            
        except FileNotFoundError:
            print(f"❌ 文件不存在: {filename}")
            return False
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False

async def run_selective_tests():
    """运行选择性测试"""
    manager = SelectiveTestManager()
    
    print("🎯 ChatGPT选择性测试工具")
    print("可以自由选择要测试的样本")
    
    # 扫描测试用例
    print("\n🔍 扫描测试用例...")
    manager.scan_all_test_cases()
    
    if not manager.all_test_cases:
        print("❌ 没有找到可用的测试用例")
        return
    
    # 显示概览
    manager.show_test_cases_by_risk_type()
    
    # 选择测试用例
    print(f"\n🎯 开始选择测试用例 (最多10个)")
    if not manager.select_tests_interactive(max_tests=10):
        return
    
    # 显示最终选择
    manager.show_selected_tests()
    
    # 保存选择
    manager.save_test_selection()
    
    # 确认执行
    confirm = input("\n是否执行这些测试? (y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    # 连接浏览器并执行测试
    tester = ExistingBrowserChatGPTTester()
    
    try:
        # 连接浏览器
        print("\n🔗 连接Chrome浏览器...")
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            print("请先运行: python start_chrome_debug.py")
            return
        
        # 等待页面准备就绪
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        await tester.find_and_update_selectors()
        print("✅ 已连接到ChatGPT页面")
        
        # 执行选择的测试
        results = []
        total_tests = len(manager.selected_tests)
        
        print(f"\n🧪 开始执行 {total_tests} 个选择的测试...")
        
        for i, test_case in enumerate(manager.selected_tests, 1):
            print(f"\n[{i}/{total_tests}] 测试: {test_case['risk_type']}/{test_case['case_id']}")
            
            result = await tester.run_single_test(test_case['folder_path'])
            results.append(result)
            
            # 显示进度
            success_count = sum(1 for r in results if r.get('test_success'))
            print(f"进度: {i}/{total_tests}, 成功: {success_count}/{i}")
            
            # 测试间隔
            if i < total_tests:
                await asyncio.sleep(3)
        
        # 保存结果
        result_filename = f"selective_test_results_{int(time.time())}.json"
        with open(result_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 统计结果
        successful_tests = sum(1 for r in results if r.get('test_success'))
        saved_files = sum(1 for r in results if r.get('saved_file'))
        
        print(f"\n🎉 选择性测试完成!")
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        print(f"保存文件: {saved_files}")
        print(f"结果已保存到: {result_filename}")
        
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        await tester.close()

def main():
    """主函数"""
    asyncio.run(run_selective_tests())

if __name__ == "__main__":
    main()
