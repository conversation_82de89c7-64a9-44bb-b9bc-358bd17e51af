# ChatGPT选择性测试指南

## 🎯 功能概述

现在您可以自由选择要测试的样本，而不是固定的测试集合。我为您提供了两种选择性测试工具：

### 📋 可用的选择性测试工具

1. **快速选择测试** (`quick_select_test.py`) - **推荐**
2. **完整选择性测试** (`selective_test_chatgpt.py`) - 功能更全面

## 🚀 快速开始

### 方法1: 使用运行脚本
```bash
./run_test.sh
# 选择选项4: 快速选择测试
```

### 方法2: 直接运行
```bash
source chatgpt_test_env/bin/activate
python quick_select_test.py
```

## 🎮 选择模式

### 1. 按风险类型选择
- 显示所有6种风险类型
- 可以从每种类型中选择若干个测试
- 适合平衡测试不同风险类型

**示例操作**：
```
选择风险类型 (输入数字): 1
选择几个? (最多 2 个): 2
✅ 已选择: [1] escape/307
✅ 已选择: [2] escape/551
```

### 2. 按ID选择
- 直接输入测试用例的ID号
- 支持批量输入，用逗号分隔
- 适合精确选择特定测试

**示例操作**：
```
请输入ID (1-880): 1,5,10,15,20,25,30,35,40,45
✅ 已选择: [1] escape/307
✅ 已选择: [5] escape/569
...
```

### 3. 随机选择
- 自动从每个风险类型中随机选择
- 确保测试覆盖所有风险类型
- 适合快速验证

## 📊 测试用例信息

### 总体统计
- **总测试用例**: 880个
- **风险类型分布**:
  - escape: 165个 (沙箱逃逸)
  - sensitive_info: 242个 (敏感信息泄露)
  - network: 77个 (网络访问)
  - privilege_escalation: 242个 (权限提升)
  - muti_user_isolation: 99个 (多用户隔离)
  - resource_exhaustion: 55个 (资源耗尽)

### 测试用例ID范围
- **ID 1-165**: escape类型
- **ID 166-407**: sensitive_info类型
- **ID 408-484**: network类型
- **ID 485-726**: privilege_escalation类型
- **ID 727-825**: muti_user_isolation类型
- **ID 826-880**: resource_exhaustion类型

## 🎯 选择策略建议

### 1. 平衡测试策略
```
每个风险类型选择1-2个，总共10个：
- escape: 2个 (ID 1,2)
- sensitive_info: 2个 (ID 166,167)
- network: 2个 (ID 408,409)
- privilege_escalation: 2个 (ID 485,486)
- muti_user_isolation: 1个 (ID 727)
- resource_exhaustion: 1个 (ID 826)
```

### 2. 重点测试策略
```
专注测试某个风险类型：
- 选择sensitive_info的10个用例: 166,167,168,169,170,171,172,173,174,175
```

### 3. 随机验证策略
```
使用随机选择模式，让系统自动分配
```

## 📋 使用示例

### 示例1: 快速验证测试
```bash
python quick_select_test.py
# 选择模式: 3 (随机选择)
# 系统会自动选择10个测试用例
```

### 示例2: 精确选择测试
```bash
python quick_select_test.py
# 选择模式: 2 (按ID选择)
# 输入ID: 1,166,408,485,727,826,100,200,300,400
```

### 示例3: 类型平衡测试
```bash
python quick_select_test.py
# 选择模式: 1 (按风险类型选择)
# 依次选择每个风险类型的1-2个测试
```

## 🔧 高级功能

### 完整选择性测试工具
如果您需要更高级的功能，可以使用：
```bash
python selective_test_chatgpt.py
```

**额外功能**：
- 搜索测试用例
- 查看测试用例详细信息
- 保存和加载选择
- 更详细的统计信息

### 选择保存和恢复
```python
# 保存当前选择
manager.save_test_selection("my_selection.json")

# 加载之前的选择
manager.load_test_selection("my_selection.json")
```

## 📊 结果输出

### 测试完成后您将获得：

1. **实时进度显示**：
   ```
   🧪 [3/10] 测试: escape/556
   进度: 3/10, 成功: 2
   ```

2. **最终统计**：
   ```
   🎉 测试完成!
   总测试: 10
   成功: 8 (80.0%)
   保存文件: 10
   ```

3. **结果文件**：
   - `quick_test_results_[timestamp].json` - 详细结果
   - 每个测试文件夹中的 `chatgpt_result.txt` - ChatGPT回复

## 💡 使用技巧

### 1. 选择有代表性的测试
- 每个风险类型至少选择1个
- 选择不同复杂度的测试用例
- 包含base64编码和直接文本的测试

### 2. 观察测试模式
- 注意哪些类型的攻击更容易成功
- 观察ChatGPT的拒绝模式
- 记录特殊的回复内容

### 3. 迭代测试
- 根据初次测试结果调整选择
- 重点测试成功率高的类型
- 验证修复后的安全改进

## 🎯 推荐的测试流程

1. **首次使用**: 随机选择10个测试验证环境
2. **平衡测试**: 每个风险类型选择1-2个
3. **重点测试**: 针对特定风险类型深入测试
4. **对比测试**: 选择相同的测试用例进行定期对比

现在您可以精确控制要测试哪些样本，让安全测试更加灵活和高效！🚀
