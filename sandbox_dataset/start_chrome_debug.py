#!/usr/bin/env python3
"""
启动Chrome浏览器的调试模式
用于连接现有浏览器进行自动化测试
"""

import subprocess
import sys
import time
import os

def start_chrome_debug_mode():
    """启动Chrome调试模式"""
    print("🚀 启动Chrome浏览器调试模式")
    
    # 检查操作系统
    if sys.platform == "darwin":  # macOS
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium"
        ]
        
        chrome_path = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_path = path
                break
        
        if not chrome_path:
            print("❌ 找不到Chrome浏览器")
            print("请确保已安装Google Chrome")
            return False
        
        # 创建临时用户数据目录
        user_data_dir = "/tmp/chrome-debug-chatgpt"
        
        # Chrome启动命令
        cmd = [
            chrome_path,
            f"--remote-debugging-port=9222",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "https://chat.openai.com"
        ]
        
    elif sys.platform.startswith("win"):  # Windows
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        chrome_path = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_path = path
                break
        
        if not chrome_path:
            print("❌ 找不到Chrome浏览器")
            return False
        
        user_data_dir = r"C:\temp\chrome-debug-chatgpt"
        
        cmd = [
            chrome_path,
            "--remote-debugging-port=9222",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "https://chat.openai.com"
        ]
        
    else:  # Linux
        chrome_commands = ["google-chrome", "chromium-browser", "chromium"]
        
        chrome_cmd = None
        for cmd_name in chrome_commands:
            try:
                subprocess.run([cmd_name, "--version"], capture_output=True)
                chrome_cmd = cmd_name
                break
            except:
                continue
        
        if not chrome_cmd:
            print("❌ 找不到Chrome浏览器")
            return False
        
        user_data_dir = "/tmp/chrome-debug-chatgpt"
        
        cmd = [
            chrome_cmd,
            "--remote-debugging-port=9222",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "https://chat.openai.com"
        ]
    
    try:
        # 启动Chrome
        print(f"启动命令: {' '.join(cmd)}")
        process = subprocess.Popen(cmd)
        
        print("✅ Chrome浏览器已启动")
        print(f"📍 调试端口: 9222")
        print(f"📁 用户数据目录: {user_data_dir}")
        print("\n📋 接下来请:")
        print("1. 在打开的浏览器中登录ChatGPT")
        print("2. 确保页面完全加载")
        print("3. 然后运行测试脚本:")
        print("   python test_chatgpt_existing_browser.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动Chrome失败: {e}")
        return False

def check_chrome_debug_connection():
    """检查Chrome调试连接"""
    try:
        import requests
        response = requests.get("http://localhost:9222/json", timeout=5)
        if response.status_code == 200:
            tabs = response.json()
            print(f"✅ Chrome调试端口连接成功，发现 {len(tabs)} 个标签页")
            
            # 查找ChatGPT标签页
            chatgpt_tabs = [tab for tab in tabs if "chat.openai.com" in tab.get("url", "")]
            if chatgpt_tabs:
                print(f"✅ 发现 {len(chatgpt_tabs)} 个ChatGPT标签页")
            else:
                print("⚠️  未发现ChatGPT标签页，请手动打开 https://chat.openai.com")
            
            return True
        else:
            print("❌ Chrome调试端口连接失败")
            return False
    except Exception as e:
        print(f"❌ 检查连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Chrome调试模式启动工具")
    print("这个工具将启动Chrome浏览器的调试模式，用于自动化测试")
    
    choice = input("\n请选择操作:\n1. 启动Chrome调试模式\n2. 检查现有连接\n3. 使用现有浏览器\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        if start_chrome_debug_mode():
            print("\n等待3秒后检查连接...")
            time.sleep(3)
            check_chrome_debug_connection()
    
    elif choice == "2":
        check_chrome_debug_connection()
    
    elif choice == "3":
        print("\n📋 使用现有浏览器的步骤:")
        print("1. 打开Chrome浏览器")
        print("2. 登录ChatGPT (https://chat.openai.com)")
        print("3. 关闭Chrome浏览器")
        print("4. 重新运行此脚本选择选项1")
        print("5. 或者直接运行: python test_chatgpt_existing_browser.py")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
