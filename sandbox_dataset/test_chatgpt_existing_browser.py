#!/usr/bin/env python3
"""
使用现有Chrome浏览器进行ChatGPT测试
连接到您已经登录的Chrome浏览器实例
"""

import asyncio
import json
import time
from pathlib import Path
from playwright.async_api import async_playwright
import logging
import subprocess
import sys

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExistingBrowserChatGPTTester:
    """使用现有浏览器的ChatGPT测试器"""
    
    def __init__(self, debug_port=9222):
        self.debug_port = debug_port
        self.browser = None
        self.page = None
        self.context = None
        
        # 更新的选择器配置
        self.selectors = {
            'upload_selector': 'input[type="file"]',
            'message_input': 'textarea[data-id="root"]',
            'send_button': 'button[data-testid="send-button"]',
            'response_selector': '[data-message-author-role="assistant"]',
            'attach_button': 'button[aria-label*="Attach"]',
            'new_chat_button': 'a[href="/"]'
        }
    
    def start_chrome_with_debug(self):
        """启动带有调试端口的Chrome浏览器"""
        try:
            print(f"🚀 启动Chrome浏览器 (调试端口: {self.debug_port})")
            print("请在打开的浏览器中手动登录ChatGPT")
            
            # Chrome启动命令
            chrome_cmd = [
                "open", "-a", "Google Chrome",
                "--args",
                f"--remote-debugging-port={self.debug_port}",
                "--user-data-dir=/tmp/chrome-debug",
                "https://chat.openai.com"
            ]
            
            subprocess.Popen(chrome_cmd)
            
            print("✅ Chrome浏览器已启动")
            print("📋 请在浏览器中:")
            print("   1. 登录ChatGPT")
            print("   2. 确保页面完全加载")
            print("   3. 然后按Enter继续...")
            
            input()
            return True
            
        except Exception as e:
            print(f"❌ 启动Chrome失败: {e}")
            return False
    
    async def connect_to_existing_browser(self):
        """连接到现有的Chrome浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 连接到现有的Chrome实例
            self.browser = await self.playwright.chromium.connect_over_cdp(
                f"http://localhost:{self.debug_port}"
            )
            
            # 获取现有的上下文和页面
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
                pages = self.context.pages
                
                # 查找ChatGPT页面
                chatgpt_page = None
                for page in pages:
                    url = page.url
                    if "chat.openai.com" in url:
                        chatgpt_page = page
                        break
                
                if chatgpt_page:
                    self.page = chatgpt_page
                    logger.info("✅ 已连接到现有的ChatGPT页面")
                else:
                    # 如果没有ChatGPT页面，创建新页面
                    self.page = await self.context.new_page()
                    await self.page.goto("https://chat.openai.com")
                    logger.info("✅ 已创建新的ChatGPT页面")
            else:
                # 创建新的上下文
                self.context = await self.browser.new_context()
                self.page = await self.context.new_page()
                await self.page.goto("https://chat.openai.com")
                logger.info("✅ 已创建新的浏览器上下文")
            
            return True
            
        except Exception as e:
            logger.error(f"连接到现有浏览器失败: {e}")
            return False
    
    async def wait_for_chatgpt_ready(self):
        """等待ChatGPT页面准备就绪"""
        try:
            logger.info("等待ChatGPT页面准备就绪...")
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            
            # 尝试多种输入框选择器
            input_selectors = [
                'textarea[data-id="root"]',
                '#prompt-textarea',
                'textarea[placeholder*="Message"]',
                '[contenteditable="true"]',
                'textarea'
            ]
            
            for selector in input_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    logger.info(f"✅ 找到输入框: {selector}")
                    self.selectors['message_input'] = selector
                    return True
                except:
                    continue
            
            logger.warning("未找到消息输入框，但继续执行")
            return True

        except Exception as e:
            logger.error(f"ChatGPT页面未准备就绪: {e}")
            return False

    async def start_new_chat(self):
        """开始新的对话"""
        try:
            logger.info("🔄 开始新对话...")

            # 多种新对话按钮选择器
            new_chat_selectors = [
                'a[href="/"]',  # 主页链接
                'button[aria-label*="New chat"]',
                'button:has-text("New chat")',
                '[data-testid="new-chat-button"]',
                'a:has-text("New chat")',
                '.new-chat-button',
                'button[title*="New"]',
                'a[title*="New chat"]',
                'nav a[href="/"]',  # 导航栏中的主页链接
                '.sidebar a[href="/"]'  # 侧边栏中的主页链接
            ]

            success = False
            for selector in new_chat_selectors:
                try:
                    new_chat_btn = await self.page.query_selector(selector)
                    if new_chat_btn:
                        # 检查元素是否可见和可点击
                        is_visible = await new_chat_btn.is_visible()
                        if is_visible:
                            await new_chat_btn.click()
                            await self.page.wait_for_timeout(3000)  # 等待页面加载
                            logger.info(f"✅ 成功开始新对话 (使用选择器: {selector})")
                            success = True
                            break
                except Exception as e:
                    logger.debug(f"尝试选择器 {selector} 失败: {e}")
                    continue

            if not success:
                # 尝试键盘快捷键
                try:
                    await self.page.keyboard.press('Control+Shift+O')  # ChatGPT的新对话快捷键
                    await self.page.wait_for_timeout(2000)
                    logger.info("✅ 通过快捷键开始新对话")
                    success = True
                except:
                    pass

            if not success:
                # 尝试直接导航到主页
                try:
                    current_url = self.page.url
                    if "chat.openai.com" in current_url:
                        await self.page.goto("https://chat.openai.com/")
                        await self.page.wait_for_timeout(5000)
                        logger.info("✅ 通过导航到主页开始新对话")
                        success = True
                except:
                    pass

            if not success:
                logger.warning("⚠️ 无法开始新对话，将在当前对话中继续")

            # 等待页面稳定
            await self.page.wait_for_timeout(2000)

            return success

        except Exception as e:
            logger.warning(f"⚠️ 开始新对话失败: {e}")
            return False
    
    async def find_and_update_selectors(self):
        """动态查找并更新选择器"""
        logger.info("🔍 动态查找页面元素...")
        
        # 查找文件上传
        upload_selectors = [
            'input[type="file"]',
            'button[aria-label*="Attach"]',
            'button[aria-label*="attach"]',
            '[data-testid="attach"]'
        ]
        
        for selector in upload_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    self.selectors['upload_selector'] = selector
                    logger.info(f"✅ 找到上传元素: {selector}")
                    break
            except:
                continue
        
        # 查找发送按钮
        send_selectors = [
            'button[data-testid="send-button"]',
            'button[aria-label*="Send"]',
            'button:has-text("Send")',
            'button[type="submit"]'
        ]
        
        for selector in send_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    self.selectors['send_button'] = selector
                    logger.info(f"✅ 找到发送按钮: {selector}")
                    break
            except:
                continue
        
        # 查找回复区域
        response_selectors = [
            '[data-message-author-role="assistant"]',
            '.message-content',
            '.assistant-message',
            '.ai-message'
        ]
        
        for selector in response_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    self.selectors['response_selector'] = selector
                    logger.info(f"✅ 找到回复区域: {selector}")
                    break
            except:
                continue
    
    async def upload_file_flexible(self, file_path):
        """灵活的文件上传方法"""
        try:
            logger.info(f"尝试上传文件: {file_path}")
            
            # 方法1: 直接查找文件输入
            file_inputs = await self.page.query_selector_all('input[type="file"]')
            if file_inputs:
                # 可能有多个文件输入，选择可见的
                for file_input in file_inputs:
                    try:
                        is_visible = await file_input.is_visible()
                        if is_visible or True:  # 即使不可见也尝试
                            await file_input.set_input_files(str(file_path))
                            logger.info("✅ 文件上传成功 (直接方式)")
                            await self.page.wait_for_timeout(2000)
                            return True
                    except:
                        continue
            
            # 方法2: 查找并点击附件按钮
            attach_selectors = [
                'button[aria-label*="Attach"]',
                'button[aria-label*="attach"]',
                'button[title*="attach"]',
                '[data-testid="attach"]',
                '.attach-button'
            ]
            
            for selector in attach_selectors:
                try:
                    attach_btn = await self.page.query_selector(selector)
                    if attach_btn:
                        await attach_btn.click()
                        await self.page.wait_for_timeout(1000)
                        
                        # 再次查找文件输入
                        file_input = await self.page.query_selector('input[type="file"]')
                        if file_input:
                            await file_input.set_input_files(str(file_path))
                            logger.info(f"✅ 文件上传成功 (通过按钮: {selector})")
                            await self.page.wait_for_timeout(2000)
                            return True
                except:
                    continue
            
            logger.warning("❌ 文件上传失败，继续测试...")
            return False
            
        except Exception as e:
            logger.error(f"文件上传异常: {e}")
            return False
    
    async def send_message_flexible(self, message):
        """灵活的消息发送方法"""
        try:
            # 查找输入框
            input_selectors = [
                'textarea[data-id="root"]',
                '#prompt-textarea',
                'textarea[placeholder*="Message"]',
                '[contenteditable="true"]',
                'textarea',
                '.ProseMirror'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    input_element = await self.page.query_selector(selector)
                    if input_element:
                        logger.info(f"找到输入框: {selector}")
                        break
                except:
                    continue
            
            if not input_element:
                logger.error("找不到消息输入框")
                return False
            
            # 输入消息
            await input_element.click()
            await self.page.wait_for_timeout(500)
            await input_element.fill("")  # 清空
            await input_element.type(message, delay=50)
            
            # 查找并点击发送按钮
            send_selectors = [
                'button[data-testid="send-button"]',
                'button[aria-label*="Send"]',
                'button:has-text("Send")',
                'button[type="submit"]'
            ]
            
            for selector in send_selectors:
                try:
                    send_button = await self.page.query_selector(selector)
                    if send_button:
                        is_enabled = await send_button.is_enabled()
                        if is_enabled:
                            await send_button.click()
                            logger.info(f"✅ 消息发送成功 (按钮: {selector})")
                            await self.page.wait_for_timeout(2000)
                            return True
                except:
                    continue
            
            # 如果找不到发送按钮，尝试按Enter
            await self.page.keyboard.press('Enter')
            logger.info("✅ 消息发送成功 (Enter键)")
            await self.page.wait_for_timeout(2000)
            return True
            
        except Exception as e:
            logger.error(f"消息发送失败: {e}")
            return False
    
    async def wait_for_response_flexible(self, max_wait_time=120):
        """灵活的回复等待方法"""
        try:
            logger.info("等待ChatGPT回复...")
            start_time = time.time()
            last_response_length = 0
            stable_count = 0
            
            response_selectors = [
                '[data-message-author-role="assistant"]',
                '.message-content',
                '.assistant-message',
                '.ai-message'
            ]
            
            while time.time() - start_time < max_wait_time:
                for selector in response_selectors:
                    try:
                        elements = await self.page.query_selector_all(selector)
                        if elements:
                            # 获取最新回复
                            latest_element = elements[-1]
                            response_text = await latest_element.text_content()
                            
                            if response_text and response_text.strip():
                                current_length = len(response_text)
                                
                                if current_length == last_response_length:
                                    stable_count += 1
                                    if stable_count >= 3:  # 稳定3秒
                                        logger.info(f"✅ 收到完整回复 (长度: {current_length})")
                                        return response_text
                                else:
                                    stable_count = 0
                                    last_response_length = current_length
                                    logger.info(f"回复生成中... (当前长度: {current_length})")
                                
                                break
                    except:
                        continue
                
                await asyncio.sleep(1)
            
            logger.warning("等待回复超时")
            return ""
            
        except Exception as e:
            logger.error(f"获取回复失败: {e}")
            return ""
    
    async def save_chatgpt_response(self, test_folder, response, upload_success, prompt, new_chat_started=False):
        """保存ChatGPT回复到测试文件夹"""
        try:
            result_file = Path(test_folder) / "chatgpt_result.txt"

            # 创建详细的结果内容
            result_content = f"""=== ChatGPT测试结果 ===
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
测试用例ID: {Path(test_folder).name}
风险类型: {Path(test_folder).parent.name}
文件上传成功: {'是' if upload_success else '否'}
新对话开始: {'是' if new_chat_started else '否'}

=== 测试独立性 ===
上下文隔离: {'已确保' if new_chat_started else '可能存在污染'}
测试可靠性: {'高' if new_chat_started else '中等'}

=== 原始提示词 ===
{prompt}

=== ChatGPT回复 ===
{response}

=== 回复统计 ===
回复长度: {len(response)} 字符
回复行数: {len(response.splitlines())} 行
是否有回复: {'是' if response.strip() else '否'}
"""

            # 保存到文件
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(result_content)

            logger.info(f"✅ ChatGPT回复已保存到: {result_file}")
            return str(result_file)

        except Exception as e:
            logger.error(f"保存ChatGPT回复失败: {e}")
            return None

    async def run_single_test(self, test_folder):
        """运行单个测试用例"""
        try:
            # 读取测试文件
            prompt_file = Path(test_folder) / "prompt.txt"
            excel_file = Path(test_folder) / "excel.csv"

            if not prompt_file.exists() or not excel_file.exists():
                logger.error(f"测试文件不存在: {test_folder}")
                return {'test_success': False, 'error': '测试文件不存在'}

            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()

            logger.info(f"🧪 开始测试: {Path(test_folder).name}")
            logger.info(f"提示词: {prompt[:100]}...")

            # 🔄 重要：每次测试前开始新对话，避免上下文污染
            new_chat_success = await self.start_new_chat()
            if new_chat_success:
                logger.info("✅ 已开始新对话，避免上下文污染")
            else:
                logger.warning("⚠️ 未能开始新对话，可能存在上下文污染")

            # 上传文件
            upload_success = await self.upload_file_flexible(excel_file)
            if not upload_success:
                logger.warning("文件上传失败，仅发送提示词")

            # 发送消息
            if not await self.send_message_flexible(prompt):
                logger.error("消息发送失败")
                return {'test_success': False, 'error': '消息发送失败'}

            # 等待回复
            response = await self.wait_for_response_flexible()

            # 保存ChatGPT回复到文件
            saved_file = await self.save_chatgpt_response(test_folder, response, upload_success, prompt, new_chat_success)

            # 分析结果
            result = {
                'test_folder': str(test_folder),
                'test_case_id': Path(test_folder).name,
                'risk_type': Path(test_folder).parent.name,
                'response_length': len(response),
                'test_success': len(response) > 0,
                'upload_success': upload_success,
                'response_preview': response[:300] + '...' if len(response) > 300 else response,
                'full_response': response,
                'saved_file': saved_file,
                'new_chat_started': new_chat_success
            }

            logger.info(f"✅ 测试完成: {result['test_success']}")
            return result

        except Exception as e:
            logger.error(f"测试执行失败: {e}")
            return {
                'test_folder': str(test_folder),
                'test_success': False,
                'error': str(e)
            }
    
    async def run_limited_tests(self, max_tests=5):
        """运行限量测试"""
        results = []
        data_path = Path("data_folder")
        test_count = 0
        
        logger.info(f"开始限量测试，最多 {max_tests} 个用例")
        
        for risk_type_folder in data_path.iterdir():
            if risk_type_folder.is_dir() and test_count < max_tests:
                # 每个风险类型测试1个用例
                test_cases = list(risk_type_folder.iterdir())[:1]
                
                for test_case_folder in test_cases:
                    if test_case_folder.is_dir() and test_count < max_tests:
                        result = await self.run_single_test(test_case_folder)
                        results.append(result)
                        test_count += 1
                        
                        # 测试间隔
                        await asyncio.sleep(3)
                        
                        if test_count >= max_tests:
                            break
        
        return results
    
    async def close(self):
        """关闭连接（不关闭浏览器）"""
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
        logger.info("已断开与浏览器的连接")

async def main():
    """主函数"""
    print("🚀 使用现有Chrome浏览器进行ChatGPT测试")
    
    tester = ExistingBrowserChatGPTTester()
    
    try:
        # 方式1: 连接现有浏览器
        print("尝试连接现有的Chrome浏览器...")
        if not await tester.connect_to_existing_browser():
            # 方式2: 启动新的Chrome实例
            print("连接失败，启动新的Chrome实例...")
            if not tester.start_chrome_with_debug():
                print("❌ 无法启动Chrome浏览器")
                return
            
            # 重新尝试连接
            await asyncio.sleep(3)
            if not await tester.connect_to_existing_browser():
                print("❌ 仍然无法连接到Chrome浏览器")
                return
        
        # 等待页面准备就绪
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        # 动态查找选择器
        await tester.find_and_update_selectors()
        
        print("✅ 已连接到ChatGPT页面")
        
        # 运行测试
        choice = input("\n选择测试规模:\n1. 单个测试\n2. 限量测试 (5个用例)\n请输入选择 (1-2): ").strip()
        
        if choice == "1":
            # 单个测试
            test_folder = "data_folder/network/1"
            if Path(test_folder).exists():
                result = await tester.run_single_test(test_folder)
                print(f"\n=== 单个测试结果 ===")
                print(f"测试用例: {result.get('test_case_id', 'N/A')}")
                print(f"风险类型: {result.get('risk_type', 'N/A')}")
                print(f"测试成功: {'是' if result.get('test_success') else '否'}")
                print(f"文件上传: {'成功' if result.get('upload_success') else '失败'}")
                print(f"回复长度: {result.get('response_length', 0)} 字符")
                if result.get('saved_file'):
                    print(f"回复已保存到: {result['saved_file']}")
                print(f"\n详细结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            else:
                print("测试文件夹不存在")
        else:
            # 限量测试
            results = await tester.run_limited_tests(max_tests=5)
            
            # 保存结果
            with open("existing_browser_test_results.json", 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            # 统计结果
            total_tests = len(results)
            successful_tests = sum(1 for r in results if r.get('test_success', False))
            saved_files = sum(1 for r in results if r.get('saved_file'))

            print(f"\n=== 测试结果统计 ===")
            print(f"总测试数: {total_tests}")
            print(f"成功测试数: {successful_tests}")
            print(f"成功率: {successful_tests/total_tests*100:.1f}%")
            print(f"已保存回复文件数: {saved_files}")
            print(f"结果已保存到: existing_browser_test_results.json")

            # 显示保存的文件列表
            if saved_files > 0:
                print(f"\n📁 ChatGPT回复文件已保存到:")
                for result in results:
                    if result.get('saved_file'):
                        print(f"  {result['saved_file']}")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
