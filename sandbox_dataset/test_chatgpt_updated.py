#!/usr/bin/env python3
"""
更新版ChatGPT测试脚本
使用最新的选择器配置
"""

import asyncio
import json
import time
from pathlib import Path
from automated_testing_playwright import PlaywrightLLMTester
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UpdatedChatGPTTester(PlaywrightLLMTester):
    """更新版ChatGPT测试器"""
    
    def __init__(self, headless=False):
        super().__init__(headless=headless, timeout=60000)
        
        # 更新的选择器配置
        self.selectors = {
            'upload_selector': 'input[type="file"]',
            'message_input': 'textarea[data-id="root"]',
            'send_button': 'button[data-testid="send-button"]',
            'response_selector': '[data-message-author-role="assistant"]',
            'attach_button': 'button[aria-label="Attach files"]',
            'new_chat_button': 'a[href="/"]'
        }
    
    async def upload_file_updated(self, file_path):
        """更新的文件上传方法"""
        try:
            logger.info(f"尝试上传文件: {file_path}")
            
            # 方法1: 直接查找文件输入
            file_inputs = await self.page.query_selector_all('input[type="file"]')
            if file_inputs:
                await file_inputs[0].set_input_files(str(file_path))
                logger.info("✅ 方法1成功: 直接文件上传")
                await self.page.wait_for_timeout(3000)
                return True
            
            # 方法2: 查找附件按钮
            attach_selectors = [
                'button[aria-label*="Attach"]',
                'button[title*="attach"]',
                '[data-testid="attach"]',
                '.attach-button'
            ]
            
            for selector in attach_selectors:
                try:
                    attach_btn = await self.page.query_selector(selector)
                    if attach_btn:
                        await attach_btn.click()
                        await self.page.wait_for_timeout(1000)
                        
                        # 再次查找文件输入
                        file_input = await self.page.query_selector('input[type="file"]')
                        if file_input:
                            await file_input.set_input_files(str(file_path))
                            logger.info(f"✅ 方法2成功: 通过附件按钮 {selector}")
                            await self.page.wait_for_timeout(3000)
                            return True
                except:
                    continue
            
            # 方法3: 拖拽上传
            try:
                # 查找可拖拽的区域
                drop_areas = await self.page.query_selector_all('[data-testid="composer-text-input"], textarea, .ProseMirror')
                if drop_areas:
                    # 模拟文件拖拽
                    await drop_areas[0].dispatch_event('dragover')
                    await drop_areas[0].dispatch_event('drop', {
                        'dataTransfer': {
                            'files': [str(file_path)]
                        }
                    })
                    logger.info("✅ 方法3成功: 拖拽上传")
                    await self.page.wait_for_timeout(3000)
                    return True
            except:
                pass
            
            logger.warning("❌ 所有上传方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            return False
    
    async def send_message_updated(self, message):
        """更新的消息发送方法"""
        try:
            # 多种输入框选择器
            input_selectors = [
                'textarea[data-id="root"]',
                '#prompt-textarea',
                'textarea[placeholder*="Message"]',
                '[contenteditable="true"]',
                'textarea',
                '.ProseMirror'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    input_element = await self.page.query_selector(selector)
                    if input_element:
                        logger.info(f"找到输入框: {selector}")
                        break
                except:
                    continue
            
            if not input_element:
                logger.error("找不到消息输入框")
                return False
            
            # 清空并输入消息
            await input_element.click()
            await self.page.keyboard.press('Control+A')
            await input_element.fill(message)
            
            # 查找发送按钮
            send_selectors = [
                'button[data-testid="send-button"]',
                'button[aria-label*="Send"]',
                'button:has-text("Send")',
                '.send-button'
            ]
            
            send_button = None
            for selector in send_selectors:
                try:
                    send_button = await self.page.query_selector(selector)
                    if send_button:
                        logger.info(f"找到发送按钮: {selector}")
                        break
                except:
                    continue
            
            if send_button:
                await send_button.click()
                logger.info("✅ 消息发送成功")
                await self.page.wait_for_timeout(2000)
                return True
            else:
                # 尝试按Enter发送
                await self.page.keyboard.press('Enter')
                logger.info("✅ 通过Enter发送消息")
                await self.page.wait_for_timeout(2000)
                return True
                
        except Exception as e:
            logger.error(f"消息发送失败: {e}")
            return False
    
    async def run_single_test_updated(self, test_folder):
        """更新的单个测试方法"""
        try:
            # 读取测试文件
            prompt_file = Path(test_folder) / "prompt.txt"
            excel_file = Path(test_folder) / "excel.csv"
            
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            
            logger.info(f"开始测试: {test_folder}")
            
            # 上传文件
            if not await self.upload_file_updated(excel_file):
                logger.warning("文件上传失败，继续测试...")
            
            # 发送消息
            if not await self.send_message_updated(prompt):
                logger.error("消息发送失败")
                return {'test_success': False, 'error': '消息发送失败'}
            
            # 等待回复
            response = await self.wait_for_response('[data-message-author-role="assistant"]', max_wait_time=120)
            
            # 分析结果
            result = {
                'test_folder': str(test_folder),
                'test_case_id': Path(test_folder).name,
                'risk_type': Path(test_folder).parent.name,
                'response_length': len(response),
                'test_success': len(response) > 0,
                'response_preview': response[:200] + '...' if len(response) > 200 else response
            }
            
            return result
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            return {'test_success': False, 'error': str(e)}

async def main():
    """主函数"""
    print("🚀 运行更新版ChatGPT测试")
    
    tester = UpdatedChatGPTTester(headless=False)
    
    try:
        await tester.setup()
        await tester.navigate_to_chat("https://chat.openai.com")
        
        print("请确保已登录ChatGPT，然后按Enter继续...")
        input()
        
        # 测试单个用例
        test_folder = "data_folder/network/1"
        if Path(test_folder).exists():
            result = await tester.run_single_test_updated(test_folder)
            print(f"测试结果: {result}")
        else:
            print("测试文件夹不存在")
        
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
