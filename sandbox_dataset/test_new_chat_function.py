#!/usr/bin/env python3
"""
测试新对话功能
验证每次测试前是否能成功开始新对话
"""

import asyncio
import time
from test_chatgpt_existing_browser import ExistingBrowserChatGPTTester

async def test_new_chat_functionality():
    """测试新对话功能"""
    print("🧪 测试ChatGPT新对话功能")
    
    tester = ExistingBrowserChatGPTTester()
    
    try:
        # 连接浏览器
        print("🔗 连接Chrome浏览器...")
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            print("请先运行: python start_chrome_debug.py")
            return
        
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        await tester.find_and_update_selectors()
        print("✅ 已连接到ChatGPT页面")
        
        # 测试新对话功能
        print(f"\n🔄 测试新对话功能...")
        
        for i in range(3):
            print(f"\n--- 第 {i+1} 次新对话测试 ---")
            
            # 开始新对话
            success = await tester.start_new_chat()
            if success:
                print("✅ 新对话开始成功")
            else:
                print("❌ 新对话开始失败")
            
            # 发送一个简单的测试消息
            test_message = f"这是第{i+1}次测试，请简单回复'收到'"
            print(f"📤 发送测试消息: {test_message}")
            
            if await tester.send_message_flexible(test_message):
                print("✅ 消息发送成功")
                
                # 等待回复
                response = await tester.wait_for_response_flexible(max_wait_time=30)
                if response:
                    print(f"📥 收到回复: {response[:100]}...")
                else:
                    print("❌ 未收到回复")
            else:
                print("❌ 消息发送失败")
            
            # 等待间隔
            if i < 2:
                print("⏱️ 等待3秒...")
                await asyncio.sleep(3)
        
        print(f"\n🎉 新对话功能测试完成!")
        
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        await tester.close()

async def test_context_isolation():
    """测试上下文隔离效果"""
    print("\n🧪 测试上下文隔离效果")
    
    tester = ExistingBrowserChatGPTTester()
    
    try:
        # 连接浏览器
        if not await tester.connect_to_existing_browser():
            print("❌ 无法连接到Chrome浏览器")
            return
        
        if not await tester.wait_for_chatgpt_ready():
            print("❌ ChatGPT页面未准备就绪")
            return
        
        await tester.find_and_update_selectors()
        
        # 第一轮对话：设置上下文
        print("\n--- 第一轮对话：设置上下文 ---")
        await tester.start_new_chat()
        
        context_message = "请记住：我的名字是Alice，我是一名安全研究员"
        print(f"📤 设置上下文: {context_message}")
        
        if await tester.send_message_flexible(context_message):
            response1 = await tester.wait_for_response_flexible(max_wait_time=30)
            print(f"📥 回复1: {response1[:100]}...")
        
        # 第二轮对话：不开始新对话，测试上下文保持
        print("\n--- 第二轮对话：测试上下文保持 ---")
        
        context_test_message = "你还记得我的名字吗？"
        print(f"📤 测试上下文: {context_test_message}")
        
        if await tester.send_message_flexible(context_test_message):
            response2 = await tester.wait_for_response_flexible(max_wait_time=30)
            print(f"📥 回复2: {response2[:100]}...")
            
            if "Alice" in response2:
                print("✅ 上下文保持正常")
            else:
                print("⚠️ 上下文可能未保持")
        
        # 第三轮对话：开始新对话，测试上下文清除
        print("\n--- 第三轮对话：开始新对话，测试上下文清除 ---")
        
        new_chat_success = await tester.start_new_chat()
        if new_chat_success:
            print("✅ 新对话已开始")
        
        context_test_message2 = "你还记得我的名字吗？"
        print(f"📤 测试上下文清除: {context_test_message2}")
        
        if await tester.send_message_flexible(context_test_message2):
            response3 = await tester.wait_for_response_flexible(max_wait_time=30)
            print(f"📥 回复3: {response3[:100]}...")
            
            if "Alice" not in response3:
                print("✅ 上下文成功清除，新对话功能正常")
            else:
                print("⚠️ 上下文可能未清除，新对话功能可能有问题")
        
        print(f"\n📊 上下文隔离测试结果:")
        print(f"  第二轮对话（应该记住）: {'记住' if 'Alice' in response2 else '未记住'}")
        print(f"  第三轮对话（应该忘记）: {'忘记' if 'Alice' not in response3 else '未忘记'}")
        
        if "Alice" in response2 and "Alice" not in response3:
            print("🎉 上下文隔离功能完全正常！")
        elif "Alice" not in response2 and "Alice" not in response3:
            print("⚠️ 上下文功能可能有问题，或者ChatGPT拒绝记住信息")
        elif "Alice" in response2 and "Alice" in response3:
            print("❌ 新对话功能可能无效，上下文未被清除")
        else:
            print("🤔 测试结果不确定，需要人工检查")
        
    except Exception as e:
        print(f"❌ 上下文隔离测试失败: {e}")
    finally:
        await tester.close()

async def main():
    """主函数"""
    print("🚀 ChatGPT新对话功能测试工具")
    
    choice = input("\n请选择测试类型:\n1. 基础新对话功能测试\n2. 上下文隔离效果测试\n3. 两个都测试\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        await test_new_chat_functionality()
    elif choice == "2":
        await test_context_isolation()
    elif choice == "3":
        await test_new_chat_functionality()
        print("\n" + "="*50)
        await test_context_isolation()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    asyncio.run(main())
